LoftUP Sampler Test Results
===============================

Configuration:
- Backbone: dinov2
- Backbone type: vit_small_reg
- Input size: 448
- Feature dimension: 768
- Number of test images: 3

Feature Extraction Results:
- Patch features shape: torch.Size([3, 1024, 384])
- Dense features shape (original): torch.Size([3, 384, 448, 448])
- Dense features shape (LoftUP): torch.Size([3, 384, 448, 448])
- Sparse features shape: [tensor([[ 0.0389,  0.3687, -0.2921,  ..., -0.0831,  0.2440,  0.2599],
        [ 0.1142,  0.2778, -0.2964,  ..., -0.0977,  0.2773,  0.2556],
        [ 0.1804,  0.2748, -0.3409,  ..., -0.1000,  0.2722,  0.2609],
        ...,
        [ 0.1265,  1.0609,  0.7539,  ..., -0.2029,  0.0670,  0.2141],
        [ 0.2590,  1.0657,  0.7842,  ..., -0.1667,  0.0284,  0.1239],
        [ 0.2128,  1.1225,  0.6779,  ..., -0.1892,  0.0281,  0.2065]],
       device='cuda:0'), tensor([[-0.0430,  0.8726, -0.2844,  ..., -0.9647, -0.5427, -0.4128],
        [-0.0572,  0.8055, -0.2100,  ..., -0.9943, -0.5232, -0.4499],
        [-0.0519,  0.8209, -0.3600,  ..., -0.9617, -0.5716, -0.3447],
        ...,
        [-0.1561,  0.6288, -0.3285,  ..., -0.4843, -0.8203, -0.7757],
        [-0.2803,  0.6041, -0.4725,  ..., -0.5167, -0.7898, -0.6051],
        [-0.2477,  0.6025, -0.4736,  ..., -0.4957, -0.7516, -0.7336]],
       device='cuda:0'), tensor([[-0.1442,  1.0230,  0.2278,  ..., -0.3247,  0.3750, -0.1522],
        [-0.0523,  1.0067,  0.2187,  ..., -0.3328,  0.4243, -0.1755],
        [ 0.0057,  1.0503,  0.1874,  ..., -0.3174,  0.4843, -0.1447],
        ...,
        [-0.5173,  0.6479,  0.2736,  ...,  0.4704, -0.2629, -0.4930],
        [-0.2776,  0.9343,  0.1614,  ..., -0.0926, -0.3387, -0.6414],
        [-0.1019,  1.2717,  0.3144,  ..., -0.2235, -0.4345, -0.6648]],
       device='cuda:0')]
- Segmentation shapes: [tensor([[  0,   0,   0,  ...,  19,  19,  19],
        [  0,   0,   0,  ...,  19,  19,  19],
        [  0,   0,   0,  ...,  19,  19,  19],
        ...,
        [373, 373, 373,  ..., 377, 377, 377],
        [373, 373, 373,  ..., 377, 377, 377],
        [373, 373, 373,  ..., 377, 377, 377]], device='cuda:0'), tensor([[  0,   0,   0,  ...,  19,  19,  19],
        [  0,   0,   0,  ...,  19,  19,  19],
        [  0,   0,   0,  ...,  19,  19,  19],
        ...,
        [385, 385, 385,  ..., 395, 395, 395],
        [385, 385, 385,  ..., 395, 395, 395],
        [385, 385, 385,  ..., 395, 395, 395]], device='cuda:0'), tensor([[  0,   0,   0,  ...,  19,  19,  19],
        [  0,   0,   0,  ...,  19,  19,  19],
        [  0,   0,   0,  ...,  19,  19,  19],
        ...,
        [388, 388, 388,  ..., 391, 391, 391],
        [388, 388, 388,  ..., 391, 391, 391],
        [388, 388, 388,  ..., 391, 391, 391]], device='cuda:0')]

Performance Analysis:
- Original resolution: 448x448
- LoftUP resolution: 448x448
- Resolution improvement: 1.00x

Results saved to: /root/loader_ws/src/debug/loftup_analysis/
