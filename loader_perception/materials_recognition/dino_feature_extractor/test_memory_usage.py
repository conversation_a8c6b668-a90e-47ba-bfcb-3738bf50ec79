 #!/usr/bin/env python3
"""
测试 DinoFeatureExtractor 的显存占用和功能
"""

import torch
import time
import gc
import sys
import os

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入DinoFeatureExtractor
from dino_feature_extractor.extractor import DinoFeatureExtractor


def get_gpu_memory_usage():
    """获取GPU显存使用情况
    
    Returns:
        tuple: (allocated_mb, cached_mb) - 已分配和缓存的显存大小(MB)
    """
    if torch.cuda.is_available():
        # 清理GPU缓存
        torch.cuda.empty_cache()
        # 获取当前GPU显存使用情况 (单位: MB)
        allocated = torch.cuda.memory_allocated() / 1024 / 1024
        cached = torch.cuda.memory_reserved() / 1024 / 1024
        return allocated, cached
    else:
        return 0, 0


def print_tensor_info(tensor, name):
    """打印tensor的详细信息
    
    Args:
        tensor: 要分析的tensor
        name (str): tensor的名称
    """
    if isinstance(tensor, torch.Tensor):
        memory_mb = tensor.numel() * tensor.element_size() / 1024 / 1024
        print(f"  {name}:")
        print(f"    - Shape: {tensor.shape}")
        print(f"    - Device: {tensor.device}")
        print(f"    - Dtype: {tensor.dtype}")
        print(f"    - Memory usage: {memory_mb:.2f} MB")
    else:
        print(f"  {name}: {type(tensor)} (non-tensor)")


def main():
    """主测试函数"""
    print("=== DinoFeatureExtractor 显存占用和功能测试 ===\n")
    
    # 检查CUDA可用性
    if torch.cuda.is_available():
        device_name = torch.cuda.get_device_name()
        cuda_version = torch.version.cuda
        print(f"CUDA设备: {device_name}")
        print(f"CUDA版本: {cuda_version}")
        print(f"PyTorch版本: {torch.__version__}")
    else:
        print("CUDA不可用，将使用CPU")
    
    # 获取初始显存使用情况
    initial_allocated, initial_cached = get_gpu_memory_usage()
    print(f"\n初始GPU显存使用:")
    print(f"  Allocated: {initial_allocated:.2f} MB")
    print(f"  Cached: {initial_cached:.2f} MB")
    
    # 1. 使用默认参数初始化DinoFeatureExtractor
    print("\n" + "="*50)
    print("1. 初始化DinoFeatureExtractor (使用默认参数)")
    print("="*50)
    
    start_time = time.time()
    
    try:
        extractor = DinoFeatureExtractor()
        init_time = time.time() - start_time
        print(f"✓ 初始化完成，耗时: {init_time:.2f}秒")
        
        # 打印配置信息
        print(f"\n配置信息:")
        print(f"  - Backbone: {extractor.backbone}")
        print(f"  - Backbone type: {extractor.backbone_type}")
        print(f"  - Input size: {extractor.input_size}")
        print(f"  - Feature dim: {extractor.feature_dim}")
        print(f"  - Patch size: {extractor.vit_patch_size}")
        print(f"  - Device: {extractor._device}")
        print(f"  - Use LoFTUP sampler: {extractor.use_loftup_sampler}")
        print(f"  - SLIC segments: {extractor.slic_n_segments}")
        print(f"  - SLIC compactness: {extractor.slic_compactness}")
        
    except Exception as e:
        print(f"✗ 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return
    
    # 2. 统计模型占用显存大小
    print("\n" + "="*50)
    print("2. 模型显存占用统计")
    print("="*50)
    
    after_init_allocated, after_init_cached = get_gpu_memory_usage()
    model_memory = after_init_allocated - initial_allocated
    
    print(f"模型加载后显存:")
    print(f"  Allocated: {after_init_allocated:.2f} MB")
    print(f"  Cached: {after_init_cached:.2f} MB")
    print(f"模型净占用显存: {model_memory:.2f} MB")
    
    # 3. 创建测试图像
    print("\n" + "="*50)
    print("3. 创建测试图像")
    print("="*50)
    
    # 创建随机测试图像 (值范围0-1)
    batch_size, channels, height, width = 1, 3, 448, 448
    test_image = torch.rand(batch_size, channels, height, width, dtype=torch.float32)
    
    if torch.cuda.is_available():
        test_image = test_image.cuda()
    
    print(f"测试图像规格: batch_size={batch_size}, channels={channels}, height={height}, width={width}")
    print_tensor_info(test_image, "test_image")
    
    # 4. 执行extract_features_and_segments
    print("\n" + "="*50)
    print("4. 执行 extract_features_and_segments")
    print("="*50)
    
    start_time = time.time()
    
    try:
        # 执行特征提取和分割
        with torch.no_grad():  # 节省显存
            results = extractor.extract_features_and_segments(test_image)
            
        extraction_time = time.time() - start_time
        print(f"✓ 特征提取完成，耗时: {extraction_time:.2f}秒")
        
        # 解包返回值
        patch_features, dense_features, sparse_features, edges, seg, centers = results
        
        # 5. 统计返回值信息
        print("\n" + "="*50)
        print("5. 返回值详细分析")
        print("="*50)
        
        print("返回值详细信息:")
        print_tensor_info(patch_features, "patch_features")
        print_tensor_info(dense_features, "dense_features") 
        print_tensor_info(sparse_features, "sparse_features")
        print_tensor_info(edges, "edges")
        print_tensor_info(seg, "segmentation")
        print_tensor_info(centers, "centers")
        
        # 计算总的返回值显存占用
        total_output_memory = 0
        tensor_count = 0
        for tensor in [patch_features, dense_features, sparse_features, edges, seg, centers]:
            if isinstance(tensor, torch.Tensor):
                tensor_memory = tensor.numel() * tensor.element_size() / 1024 / 1024
                total_output_memory += tensor_memory
                tensor_count += 1
        
        print(f"\n返回值汇总:")
        print(f"  - 总tensor数量: {tensor_count}")
        print(f"  - 总显存占用: {total_output_memory:.2f} MB")
        
        # 检查所有返回值是否在同一设备上
        devices = []
        tensor_names = ["patch_features", "dense_features", "sparse_features", "edges", "seg", "centers"]
        tensors = [patch_features, dense_features, sparse_features, edges, seg, centers]
        
        for name, tensor in zip(tensor_names, tensors):
            if isinstance(tensor, torch.Tensor):
                devices.append(str(tensor.device))
        
        unique_devices = set(devices)
        print(f"  - 设备位置: {unique_devices}")
        
        if len(unique_devices) == 1:
            print("  ✓ 所有返回值都在同一设备上")
        else:
            print("  ⚠ 警告: 返回值分布在不同设备上")
        
        # 6. 验证返回值的形状和语义
        print(f"\n返回值语义验证:")
        print(f"  - 输入图像形状: {test_image.shape}")
        print(f"  - patch_features形状: {patch_features.shape} (应为 [B, num_patches, feature_dim])")
        print(f"  - dense_features形状: {dense_features.shape} (应为 [B, feature_dim, H, W])")
        print(f"  - sparse_features形状: {sparse_features.shape} (应为 [num_segments, feature_dim])")
        print(f"  - edges形状: {edges.shape} (应为 [2, num_edges])")
        print(f"  - segmentation形状: {seg.shape} (应为 [H, W])")
        print(f"  - centers形状: {centers.shape} (应为 [num_segments, 2])")
        
        # 检查分割数量一致性
        num_segments_from_sparse = sparse_features.shape[0]
        num_segments_from_centers = centers.shape[0]
        num_segments_from_seg = seg.max().item() + 1
        
        print(f"\n分割一致性检查:")
        print(f"  - sparse_features中的分割数: {num_segments_from_sparse}")
        print(f"  - centers中的分割数: {num_segments_from_centers}")
        print(f"  - segmentation中的分割数: {num_segments_from_seg}")
        
        if num_segments_from_sparse == num_segments_from_centers == num_segments_from_seg:
            print("  ✓ 分割数量一致")
        else:
            print("  ⚠ 警告: 分割数量不一致")
            
    except Exception as e:
        print(f"✗ 特征提取失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. 最终显存使用情况
    print("\n" + "="*50)
    print("6. 最终显存使用情况")
    print("="*50)
    
    final_allocated, final_cached = get_gpu_memory_usage()
    total_memory = final_allocated - initial_allocated
    
    print(f"最终显存使用:")
    print(f"  Allocated: {final_allocated:.2f} MB")
    print(f"  Cached: {final_cached:.2f} MB")
    print(f"\n显存占用分析:")
    print(f"  - 总计占用显存: {total_memory:.2f} MB")
    print(f"  - 其中模型占用: {model_memory:.2f} MB ({model_memory/total_memory*100:.1f}%)")
    print(f"  - 数据和计算占用: {total_memory - model_memory:.2f} MB ({(total_memory - model_memory)/total_memory*100:.1f}%)")
    
    print(f"\n" + "="*50)
    print("测试完成")
    print("="*50)


if __name__ == "__main__":
    main()