#!/usr/bin/env python3
"""
Test script for DinoFeatureExtractor SLIC segmentation functionality.

This script tests the SLIC segmentation capabilities of the 
DinoFeatureExtractor class using torch_geometric with real downloaded images.

Author: ROS2 Assistant
Date: 2024
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
import requests
from PIL import Image
from pathlib import Path
import shutil
import warnings
from dino_feature_extractor import DinoFeatureExtractor

warnings.filterwarnings('ignore')


def download_image(url, save_path):
    """Download image from URL and save locally.
    
    Args:
        url (str): Image URL to download
        save_path (Path): Local path to save the image
        
    Returns:
        bool: True if download successful, False otherwise
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"Downloaded: {save_path}")
        return True
    except Exception as e:
        print(f"Failed to download {url}: {e}")
        return False


def preprocess_image_for_dino(img_pil, input_size=224):
    """Convert PIL image to tensor format expected by DINO.
    
    Args:
        img_pil (PIL.Image): Input PIL image
        input_size (int): Target input size for the model
        
    Returns:
        torch.Tensor: Preprocessed image tensor
    """
    from torchvision import transforms as T
    
    transform = T.Compose([
        T.Resize((input_size, input_size)),
        T.ToTensor(),
        # Note: DinoFeatureExtractor will apply its own normalization
    ])
    
    return transform(img_pil)


def create_test_image(height=224, width=224, pattern="gradient"):
    """Create a test image for segmentation testing.
    
    Args:
        height (int): Image height in pixels
        width (int): Image width in pixels
        pattern (str): Type of test pattern ('gradient', 'checkerboard', 'circles')
        
    Returns:
        torch.Tensor: Test image tensor of shape (1, 3, H, W)
    """
    if pattern == "gradient":
        # Create gradient pattern
        x = np.linspace(0, 1, width)
        y = np.linspace(0, 1, height)
        X, Y = np.meshgrid(x, y)
        
        # RGB gradient
        r = X
        g = Y
        b = 1 - X
        
    elif pattern == "checkerboard":
        # Create checkerboard pattern
        check_size = 32
        x = np.arange(width) // check_size
        y = np.arange(height) // check_size
        X, Y = np.meshgrid(x, y)
        pattern_2d = (X + Y) % 2
        
        r = pattern_2d
        g = 1 - pattern_2d
        b = pattern_2d * 0.5
        
    elif pattern == "circles":
        # Create concentric circles
        center_x, center_y = width // 2, height // 2
        x = np.arange(width) - center_x
        y = np.arange(height) - center_y
        X, Y = np.meshgrid(x, y)
        distance = np.sqrt(X**2 + Y**2)
        
        r = np.sin(distance / 20) * 0.5 + 0.5
        g = np.cos(distance / 30) * 0.5 + 0.5
        b = np.sin(distance / 15) * 0.5 + 0.5
        
    else:
        raise ValueError(f"Unknown pattern: {pattern}")
    
    # Stack RGB channels and add batch dimension
    img = np.stack([r, g, b], axis=0)
    img = torch.from_numpy(img).float().unsqueeze(0)  # Shape: (1, 3, H, W)
    
    return img


def test_slic_segmentation_with_real_images(image_paths, save_dir="~/loader_ws/src/debug/segmentation_results/"):
    """Test SLIC segmentation functionality with real images.
    
    Args:
        image_paths (list): List of paths to real images
        save_dir (str): Directory to save results
    """
    print("Testing SLIC segmentation with real images...")
    
    # Create save directory - properly expand user home directory (~)
    save_path = Path(save_dir).expanduser()
    save_path.mkdir(parents=True, exist_ok=True)
    print(f"Saving results to: {save_path.absolute()}")
    
    # Initialize feature extractor with SLIC segmentation
    extractor = DinoFeatureExtractor(
        device="cuda" if torch.cuda.is_available() else "cpu",
        backbone="dinov2",
        backbone_type="vit_base_reg",
        input_size=224,
        slic_n_segments=100,
        slic_compactness=20.0
    )
    
    for idx, img_path in enumerate(image_paths):
        try:
            # Load and preprocess image
            img_pil = Image.open(img_path).convert('RGB')
            print(f"Processing image {idx+1}: {img_path} - Size: {img_pil.size}")
            
            # Preprocess for DINO
            img_tensor = preprocess_image_for_dino(img_pil, input_size=224)
            img_batch = img_tensor.unsqueeze(0)  # Add batch dimension
            
            print(f"Image tensor shape: {img_batch.shape}")
            
            # Perform segmentation
            edges, seg, centers = extractor.compute_segments(img_batch)
            
            print(f"Segmentation map shape: {seg.shape}")
            print(f"Number of segments: {seg.max().item() + 1}")
            print(f"Edges shape: {edges.shape}")
            print(f"Centers shape: {centers.shape}")
            print(f"Number of edges: {edges.shape[1]}")
            
            # Visualize results
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # Original image
            img_vis = img_batch[0].permute(1, 2, 0).cpu().numpy()
            axes[0].imshow(img_vis)
            axes[0].set_title(f"Original Image {idx+1}")
            axes[0].axis('off')
            
            # Segmentation map
            seg_vis = seg.cpu().numpy()
            axes[1].imshow(seg_vis, cmap='tab20')
            axes[1].set_title(f"SLIC Segmentation ({seg.max().item() + 1} segments)")
            axes[1].axis('off')
            
            # Segment centers
            axes[2].imshow(seg_vis, cmap='gray', alpha=0.7)
            centers_np = centers.cpu().numpy()
            axes[2].scatter(centers_np[:, 1], centers_np[:, 0], c='red', s=10, alpha=0.8)
            axes[2].set_title("Segment Centers")
            axes[2].axis('off')
            
            plt.tight_layout()
            
            # Save results
            result_file = save_path / f"slic_segmentation_image_{idx+1}.png"
            plt.savefig(result_file, dpi=150, bbox_inches='tight')
            print(f"Segmentation result saved to: {result_file}")
            plt.close()  # Close figure to free memory
            
        except Exception as e:
            print(f"Error processing image {img_path}: {e}")
            continue
    
    print("SLIC segmentation test with real images completed successfully!")


def test_feature_extraction_with_segmentation_real_images(image_paths, save_dir="./segmentation_results/"):
    """Test feature extraction combined with segmentation using real images.
    
    Args:
        image_paths (list): List of paths to real images
        save_dir (str): Directory to save results
    """
    print("\nTesting feature extraction with segmentation using real images...")
    
    # Create save directory - properly expand user home directory (~) if present
    save_path = Path(save_dir).expanduser()
    save_path.mkdir(parents=True, exist_ok=True)
    
    # Initialize feature extractor
    extractor = DinoFeatureExtractor(
        device="cuda" if torch.cuda.is_available() else "cpu",
        backbone="dinov2",
        backbone_type="vit_base_reg",
        input_size=224,
        slic_n_segments=50
    )
    
    for idx, img_path in enumerate(image_paths):
        try:
            # Load and preprocess image
            img_pil = Image.open(img_path).convert('RGB')
            img_tensor = preprocess_image_for_dino(img_pil, input_size=224)
            img_batch = img_tensor.unsqueeze(0)
            
            # Extract features
            features = extractor.extract_features(img_batch)
            print(f"Image {idx+1} - Features shape: {features.shape}")
            print(f"Feature dimension: {extractor.feature_dim}")
            
            # Perform segmentation
            edges, seg, centers = extractor.compute_segments(img_batch)
            print(f"Segmentation completed with {seg.max().item() + 1} segments")
            
        except Exception as e:
            print(f"Error processing image {img_path}: {e}")
            continue
    
    print("Feature extraction with segmentation test completed successfully!")


def test_different_slic_parameters_real_images(image_paths, save_dir="./segmentation_results/"):
    """Test SLIC with different parameter configurations using real images.
    
    Args:
        image_paths (list): List of paths to real images
        save_dir (str): Directory to save results
    """
    print("\nTesting different SLIC parameter configurations with real images...")
    
    # Create save directory - properly expand user home directory (~) if present
    save_path = Path(save_dir).expanduser()
    save_path.mkdir(parents=True, exist_ok=True)
    
    # Test configurations
    configs = [
        {"n_segments": 100, "compactness": 10.0, "name": "Low segments, Low compactness"},
        {"n_segments": 500, "compactness": 30.0, "name": "Medium segments, Medium compactness"},
        {"n_segments": 1000, "compactness": 50.0, "name": "High segments, High compactness"},
    ]
    
    # Process first image for parameter comparison
    if image_paths:
        img_path = image_paths[0]
        img_pil = Image.open(img_path).convert('RGB')
        img_tensor = preprocess_image_for_dino(img_pil, input_size=224)
        img_batch = img_tensor.unsqueeze(0)
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Original image
        img_vis = img_batch[0].permute(1, 2, 0).cpu().numpy()
        axes[0, 0].imshow(img_vis)
        axes[0, 0].set_title("Original Image")
        axes[0, 0].axis('off')
        
        # Test different configurations
        for i, config in enumerate(configs):
            extractor = DinoFeatureExtractor(
                device="cuda" if torch.cuda.is_available() else "cpu",
                backbone="dinov2",
                backbone_type="vit_base_reg",
                input_size=224,
                slic_n_segments=config["n_segments"],
                slic_compactness=config["compactness"]
            )
            
            # Perform segmentation
            edges, seg, centers = extractor.compute_segments(img_batch)
            
            print(f"{config['name']}: {seg.max().item() + 1} segments, {edges.shape[1]} edges")
            
            # Plot results
            row = (i + 1) // 2
            col = (i + 1) % 2
            
            seg_vis = seg.cpu().numpy()
            axes[row, col].imshow(seg_vis, cmap='tab20')
            axes[row, col].set_title(f"{config['name']}\n({seg.max().item() + 1} segments)")
            axes[row, col].axis('off')
        
        plt.tight_layout()
        
        # Save comparison results
        comparison_file = save_path / "slic_parameter_comparison.png"
        plt.savefig(comparison_file, dpi=150, bbox_inches='tight')
        print(f"Parameter comparison saved to: {comparison_file}")
        plt.close()
    
    print("SLIC parameter testing with real images completed successfully!")


def main():
    """Main test function."""
    print("=== DinoFeatureExtractor SLIC Segmentation Test with Real Images ===\n")
    
    # Image URLs to download (same as test_extractor.py)
    image_urls = [
        "https://ml-img.oss-cn-beijing.aliyuncs.com/000030.jpg",
        "https://ml-img.oss-cn-beijing.aliyuncs.com/cat.82.jpg", 
        "https://ml-img.oss-cn-beijing.aliyuncs.com/dog.332.jpg"
    ]
    
    # Create temporary directory for downloaded images
    temp_dir = Path("./temp_images")
    temp_dir.mkdir(exist_ok=True)
    
    # Download images
    image_paths = []
    for i, url in enumerate(image_urls):
        filename = f"image_{i+1}.jpg"
        save_path = temp_dir / filename
        
        if download_image(url, save_path):
            image_paths.append(save_path)
        else:
            print(f"Skipping {url} due to download failure")
    
    if not image_paths:
        print("No images downloaded successfully. Exiting.")
        return
    
    print(f"Successfully downloaded {len(image_paths)} images")
    
    try:
        # Test basic SLIC segmentation with real images
        test_slic_segmentation_with_real_images(image_paths, save_dir="~/loader_ws/src/debug/segmentation_results/")
        
        # Test combined feature extraction and segmentation with real images
        # test_feature_extraction_with_segmentation_real_images(image_paths)
        
        # Test different SLIC parameters with real images
        test_different_slic_parameters_real_images(image_paths, save_dir="~/loader_ws/src/debug/segmentation_results/")
        
        print("\n=== All tests passed successfully! ===")
        
    except Exception as e:
        print(f"\nTest failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        # Clean up temporary files
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print("Cleaned up temporary files.")


if __name__ == "__main__":
    main() 