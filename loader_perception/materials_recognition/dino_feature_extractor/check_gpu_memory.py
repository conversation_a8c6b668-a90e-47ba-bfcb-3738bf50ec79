#!/usr/bin/env python3
"""
GPU内存检查和清理工具
用于诊断和解决CUDA内存不足问题
"""

import torch
import gc
import subprocess
import os
import psutil

def get_gpu_memory_info():
    """获取详细的GPU内存使用信息"""
    if not torch.cuda.is_available():
        print("CUDA不可用")
        return
    
    device_count = torch.cuda.device_count()
    print(f"检测到 {device_count} 个GPU设备")
    
    for i in range(device_count):
        device = torch.device(f'cuda:{i}')
        torch.cuda.set_device(device)
        
        # 获取内存信息
        total_memory = torch.cuda.get_device_properties(i).total_memory
        allocated_memory = torch.cuda.memory_allocated(i)
        cached_memory = torch.cuda.memory_reserved(i)
        free_memory = total_memory - allocated_memory
        
        print(f"\nGPU {i} ({torch.cuda.get_device_name(i)}):")
        print(f"  总内存:     {total_memory / 1024**3:.2f} GB")
        print(f"  已分配:     {allocated_memory / 1024**3:.2f} GB")
        print(f"  已缓存:     {cached_memory / 1024**3:.2f} GB")
        print(f"  可用内存:   {free_memory / 1024**3:.2f} GB")
        print(f"  使用率:     {(allocated_memory / total_memory) * 100:.1f}%")

def get_process_gpu_usage():
    """获取GPU进程使用情况"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-compute-apps=pid,process_name,used_memory', 
                               '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, check=True)
        
        print("\n占用GPU的进程:")
        print("PID\t进程名\t\t使用内存(MB)")
        print("-" * 50)
        
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = line.split(', ')
                if len(parts) >= 3:
                    pid, name, memory = parts[0], parts[1], parts[2]
                    print(f"{pid}\t{name[:20]:<20}\t{memory}")
                    
    except subprocess.CalledProcessError:
        print("无法获取GPU进程信息，请确保安装了nvidia-smi")
    except FileNotFoundError:
        print("nvidia-smi未找到，请确保安装了NVIDIA驱动")

def clear_gpu_memory():
    """清理GPU内存"""
    if not torch.cuda.is_available():
        print("CUDA不可用，无法清理GPU内存")
        return
    
    print("\n正在清理GPU内存...")
    
    # 清理PyTorch缓存
    torch.cuda.empty_cache()
    
    # 强制垃圾回收
    gc.collect()
    
    print("GPU内存清理完成")

def kill_gpu_processes():
    """终止占用GPU的进程（谨慎使用）"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-compute-apps=pid', 
                               '--format=csv,noheader,nounits'], 
                              capture_output=True, text=True, check=True)
        
        pids = []
        for line in result.stdout.strip().split('\n'):
            if line.strip() and line.strip().isdigit():
                pids.append(int(line.strip()))
        
        if not pids:
            print("没有找到占用GPU的进程")
            return
        
        print(f"找到 {len(pids)} 个占用GPU的进程")
        
        # 获取当前进程PID，避免杀死自己
        current_pid = os.getpid()
        
        for pid in pids:
            if pid == current_pid:
                print(f"跳过当前进程 PID {pid}")
                continue
                
            try:
                # 检查进程是否存在
                if psutil.pid_exists(pid):
                    process = psutil.Process(pid)
                    process_name = process.name()
                    
                    response = input(f"是否终止进程 {pid} ({process_name})? (y/N): ")
                    if response.lower() == 'y':
                        process.terminate()
                        print(f"已终止进程 {pid}")
                    else:
                        print(f"跳过进程 {pid}")
                else:
                    print(f"进程 {pid} 不存在")
                    
            except psutil.AccessDenied:
                print(f"没有权限终止进程 {pid}")
            except psutil.NoSuchProcess:
                print(f"进程 {pid} 不存在")
            except Exception as e:
                print(f"终止进程 {pid} 时出错: {e}")
                
    except subprocess.CalledProcessError:
        print("无法获取GPU进程信息")
    except FileNotFoundError:
        print("nvidia-smi未找到")

def set_memory_optimization():
    """设置内存优化环境变量"""
    print("\n设置PyTorch内存优化选项:")
    
    # 设置内存分片以减少碎片化
    os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'expandable_segments:True'
    print("已设置 PYTORCH_CUDA_ALLOC_CONF=expandable_segments:True")
    
    # 限制PyTorch使用的GPU内存
    torch.cuda.set_per_process_memory_fraction(0.8)  # 使用80%的GPU内存
    print("已限制PyTorch最多使用80%的GPU内存")

def main():
    """主函数"""
    print("=== GPU内存诊断工具 ===\n")
    
    # 检查GPU内存使用情况
    get_gpu_memory_info()
    
    # 检查GPU进程
    get_process_gpu_usage()
    
    # 设置内存优化
    set_memory_optimization()
    
    # 清理GPU内存
    clear_gpu_memory()
    
    # 再次检查内存使用情况
    print("\n清理后的GPU内存状态:")
    get_gpu_memory_info()
    
    print("\n=== 建议的解决方案 ===")
    print("1. 降低图像分辨率（从448到224或更小）")
    print("2. 减少batch size和同时处理的图像数量")
    print("3. 使用更小的模型（如dinov2的tiny版本）")
    print("4. 减少SLIC分割的segment数量")
    print("5. 在特征提取时使用torch.no_grad()")
    print("6. 及时将tensor移动到CPU并清理GPU内存")
    
    # 询问是否终止占用GPU的进程
    response = input("\n是否要查看和管理占用GPU的进程? (y/N): ")
    if response.lower() == 'y':
        kill_gpu_processes()

if __name__ == "__main__":
    main() 