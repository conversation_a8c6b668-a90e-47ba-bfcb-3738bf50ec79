import matplotlib.pyplot as plt
import torch
import torch.nn.functional as F
import torchvision.transforms.functional as TF
import numpy as np
from sklearn.decomposition import PCA
import requests
from PIL import Image
from pathlib import Path
import shutil
import warnings
from torchvision import transforms as T

# Import DINO feature extractor
from dino_feature_extractor import DinoFeatureExtractor

warnings.filterwarnings('ignore')

def _remove_axes(ax):
    ax.xaxis.set_major_formatter(plt.NullFormatter())
    ax.yaxis.set_major_formatter(plt.NullFormatter())
    ax.set_xticks([])
    ax.set_yticks([])


def remove_axes(axes):
    if len(axes.shape) == 2:
        for ax1 in axes:
            for ax in ax1:
                _remove_axes(ax)
    else:
        for ax in axes:
            _remove_axes(ax)

@torch.no_grad()
def plot_feats(image, lr, hr, save_name='feats.png'):
    """Plot original image, low-resolution features, and high-resolution upsampled features"""
    assert len(image.shape) == len(lr.shape) == len(hr.shape) == 3
    [lr_feats_pca, hr_feats_pca], _ = pca([lr.unsqueeze(0), hr.unsqueeze(0)])
    fig, ax = plt.subplots(1, 3, figsize=(12, 4))
    ax[0].imshow(image.permute(1, 2, 0).detach().cpu())
    ax[0].set_title("Image")
    ax[1].imshow(lr_feats_pca[0].permute(1, 2, 0).detach().cpu())
    ax[1].set_title("Original Features")
    ax[2].imshow(hr_feats_pca[0].permute(1, 2, 0).detach().cpu())
    ax[2].set_title("Upsampled Features")
    remove_axes(ax)
    plt.savefig(save_name, bbox_inches='tight', pad_inches=0.1)
    plt.close()
    print(f"Feature comparison saved to: {save_name}")

class ToTensorWithoutScaling:
    """Convert PIL image or numpy array to a PyTorch tensor without scaling the values."""
    def __call__(self, pic):
        # Convert the PIL Image or numpy array to a tensor (without scaling).
        return TF.pil_to_tensor(pic).long()

class TorchPCA(object):

    def __init__(self, n_components):
        self.n_components = n_components

    def fit(self, X):
        self.mean_ = X.mean(dim=0)
        unbiased = X - self.mean_.unsqueeze(0)
        U, S, V = torch.pca_lowrank(unbiased, q=self.n_components, center=False, niter=4)
        self.components_ = V.T
        self.singular_values_ = S
        return self

    def transform(self, X):
        t0 = X - self.mean_.unsqueeze(0)
        projected = t0 @ self.components_.T
        return projected


def pca(image_feats_list, dim=3, fit_pca=None, use_torch_pca=True, max_samples=None):
    """Apply PCA to features for visualization"""
    device = image_feats_list[0].device

    def flatten(tensor, target_size=None):
        if len(tensor.shape) == 2:
            return tensor.detach().cpu()
        if target_size is not None and fit_pca is None:
            tensor = F.interpolate(tensor, (target_size, target_size), mode="bilinear")
        B, C, H, W = tensor.shape
        return tensor.permute(1, 0, 2, 3).reshape(C, B * H * W).permute(1, 0).detach().cpu()

    if len(image_feats_list) > 1 and fit_pca is None:
        if len(image_feats_list[0].shape) == 2:
            target_size = None
        else:
            target_size = image_feats_list[0].shape[2]
    else:
        target_size = None

    flattened_feats = []
    for feats in image_feats_list:
        flattened_feats.append(flatten(feats, target_size))
    x = torch.cat(flattened_feats, dim=0)

    # Subsample the data if max_samples is set and the number of samples exceeds max_samples
    if max_samples is not None and x.shape[0] > max_samples:
        indices = torch.randperm(x.shape[0])[:max_samples]
        x = x[indices]

    if fit_pca is None:
        if use_torch_pca:
            fit_pca = TorchPCA(n_components=dim).fit(x)
        else:
            fit_pca = PCA(n_components=dim).fit(x)

    reduced_feats = []
    for feats in image_feats_list:
        x_red = fit_pca.transform(flatten(feats))
        if isinstance(x_red, np.ndarray):
            x_red = torch.from_numpy(x_red)
        x_red -= x_red.min(dim=0, keepdim=True).values
        x_red /= x_red.max(dim=0, keepdim=True).values
        if len(feats.shape) == 2:
            reduced_feats.append(x_red) # 1D
        else:
            B, C, H, W = feats.shape
            reduced_feats.append(x_red.reshape(B, H, W, dim).permute(0, 3, 1, 2).to(device)) # 3D

    return reduced_feats, fit_pca

class UnNormalize(object):
    """Reverse ImageNet normalization for visualization"""
    def __init__(self, mean, std):
        self.mean = mean
        self.std = std

    def __call__(self, image):
        image2 = torch.clone(image)
        if len(image2.shape) == 4:
            # batched
            image2 = image2.permute(1, 0, 2, 3)
        for t, m, s in zip(image2, self.mean, self.std):
            t.mul_(s).add_(m)
        return image2.permute(1, 0, 2, 3)

unnorm = UnNormalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])

def download_image(url, save_path):
    """Download image from URL and save locally."""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"Downloaded: {save_path}")
        return True
    except Exception as e:
        print(f"Failed to download {url}: {e}")
        return False

def preprocess_image_for_dino(img_pil, input_size=448):
    """Convert PIL image to tensor format expected by DINO."""
    transform = T.Compose([
        T.Resize((input_size, input_size)),
        T.ToTensor(),
        # Note: DinoFeatureExtractor will apply its own normalization
    ])
    
    return transform(img_pil)

def visualize_loftup_comparison(original_imgs, patch_features, dense_features_original, dense_features_loftup, save_dir="./loftup_analysis/"):
    """Visualize comparison between original and LoftUP upsampled features."""
    
    # Create save directory
    save_path = Path(save_dir).expanduser()
    save_path.mkdir(parents=True, exist_ok=True)
    
    N = len(original_imgs)
    
    # Process each image
    for idx, img in enumerate(original_imgs):
        # Get features for this image
        patch_feat = patch_features[idx]  # [num_patches, feature_dim]
        dense_feat_orig = dense_features_original[idx]  # [C, H, W]
        dense_feat_loftup = dense_features_loftup[idx]  # [C, H, W]
        
        print(f"Image {idx+1} - Patch features shape: {patch_feat.shape}")
        print(f"Image {idx+1} - Original dense features shape: {dense_feat_orig.shape}")
        print(f"Image {idx+1} - LoftUP dense features shape: {dense_feat_loftup.shape}")
        
        # Apply PCA for visualization
        [patch_pca], _ = pca([patch_feat])  # Handle 2D features
        [dense_orig_pca, dense_loftup_pca], _ = pca([dense_feat_orig.unsqueeze(0), dense_feat_loftup.unsqueeze(0)])
        
        # Create comparison plot
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        
        # Row 1: Original visualizations
        axes[0, 0].imshow(img)
        axes[0, 0].set_title(f"Original Image {idx+1}")
        axes[0, 0].axis('off')
        
        # Reshape patch features for visualization
        patch_feat_reshaped = patch_pca.reshape(int(np.sqrt(patch_feat.shape[0])), int(np.sqrt(patch_feat.shape[0])), 3)
        axes[0, 1].imshow(patch_feat_reshaped)
        axes[0, 1].set_title("Patch Features (PCA)")
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(dense_orig_pca[0].permute(1, 2, 0).cpu().numpy())
        axes[0, 2].set_title("Original Dense Features")
        axes[0, 2].axis('off')
        
        # Row 2: LoftUP comparisons
        axes[1, 0].imshow(dense_loftup_pca[0].permute(1, 2, 0).cpu().numpy())
        axes[1, 0].set_title("LoftUP Upsampled Features")
        axes[1, 0].axis('off')
        
        # Feature difference visualization
        if dense_feat_orig.shape == dense_feat_loftup.shape:
            # Compute feature similarity/difference
            feature_diff = torch.norm(dense_feat_orig - dense_feat_loftup, dim=0, p=2)
            feature_diff_norm = (feature_diff - feature_diff.min()) / (feature_diff.max() - feature_diff.min())
            axes[1, 1].imshow(feature_diff_norm.cpu().numpy(), cmap='hot')
            axes[1, 1].set_title("Feature Difference (L2 norm)")
            axes[1, 1].axis('off')
        else:
            axes[1, 1].text(0.5, 0.5, "Different shapes\ncannot compare", 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title("Feature Difference")
            axes[1, 1].axis('off')
        
        # Show resolution comparison
        orig_text = f"Original: {dense_feat_orig.shape[1]}x{dense_feat_orig.shape[2]}"
        loftup_text = f"LoftUP: {dense_feat_loftup.shape[1]}x{dense_feat_loftup.shape[2]}"
        axes[1, 2].text(0.5, 0.7, orig_text, ha='center', va='center', transform=axes[1, 2].transAxes, fontsize=12)
        axes[1, 2].text(0.5, 0.3, loftup_text, ha='center', va='center', transform=axes[1, 2].transAxes, fontsize=12)
        axes[1, 2].set_title("Resolution Comparison")
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        # Save individual image comparison
        save_file = save_path / f"loftup_comparison_image_{idx+1}.png"
        plt.savefig(save_file, dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"Comparison visualization saved to: {save_file}")
        
        # Also create the simple feature comparison using the existing plot_feats function
        if dense_feat_orig.shape[1] > 32:  # Only if resolution is reasonable for visualization
            # Downsample original features for fair comparison with patch-level features
            lr_feats_for_comparison = F.interpolate(
                dense_feat_orig.unsqueeze(0), 
                size=(32, 32), 
                mode='bilinear', 
                align_corners=False
            ).squeeze(0)
            
            # Unnormalize image for visualization
            img_tensor = preprocess_image_for_dino(img, input_size=448)
            img_normalized = T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])(img_tensor)
            img_unnormalized = unnorm(img_normalized.unsqueeze(0)).squeeze(0)
            
            plot_feats(
                img_unnormalized, 
                lr_feats_for_comparison, 
                dense_feat_loftup, 
                save_name=str(save_path / f"feats_comparison_image_{idx+1}.png")
            )

def main():
    """Main function to test LoftUP sampler with DinoFeatureExtractor."""
    # Create temporary directory for downloaded images
    temp_dir = Path("./temp_images")
    temp_dir.mkdir(exist_ok=True)
    
    # Test image URLs
    image_urls = [
        "https://ml-img.oss-cn-beijing.aliyuncs.com/000030.jpg",
        "https://ml-img.oss-cn-beijing.aliyuncs.com/cat.82.jpg", 
        "https://ml-img.oss-cn-beijing.aliyuncs.com/dog.332.jpg"
    ]
    
    # Create output directory for results
    output_dir = Path("~/loader_ws/src/debug/loftup_analysis").expanduser()
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        print("=== LoftUP Sampler Test ===")
        print("Downloading test images...")
        
        # Download images
        image_paths = []
        for i, url in enumerate(image_urls):
            filename = f"test_image_{i+1}.jpg"
            save_path = temp_dir / filename
            
            if download_image(url, save_path):
                image_paths.append(save_path)
            else:
                print(f"Skipping {url} due to download failure")
        
        if not image_paths:
            print("No images downloaded successfully. Exiting.")
            return
        
        # Load images
        original_imgs = []
        img_tensors = []
        
        for img_path in image_paths:
            try:
                img = Image.open(img_path).convert('RGB')
                original_imgs.append(img)
                
                # Preprocess for DINO
                img_tensor = preprocess_image_for_dino(img, input_size=448)
                img_tensors.append(img_tensor)
                
                print(f"Loaded image: {img_path} - Size: {img.size}")
            except Exception as e:
                print(f"Failed to load {img_path}: {e}")
        
        if not img_tensors:
            print("No images loaded successfully. Exiting.")
            return
        
        # Stack into batch
        img_batch = torch.stack(img_tensors, dim=0)
        print(f"Input batch shape: {img_batch.shape}")
        
        # Initialize DINO feature extractor without LoftUP first (for comparison)
        print("\nInitializing DINO feature extractor without LoftUP...")
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"Using device: {device}")
        
        extractor_original = DinoFeatureExtractor(
            device=device,
            backbone="dinov2",
            input_size=448,
            backbone_type="vit_small_reg",  # Use vit_small_reg as requested
            patch_size=14,
            use_loftup_sampler=False  # Disable LoftUP for comparison
        )
        
        print(f"Original extractor initialized:")
        print(f"  - Backbone: {extractor_original.backbone}")
        print(f"  - Backbone type: {extractor_original.backbone_type}")
        print(f"  - Input size: {extractor_original.input_size}")
        print(f"  - Feature dimension: {extractor_original.feature_dim}")
        print(f"  - Use LoftUP: {getattr(extractor_original, 'use_loftup_sampler', False)}")
        
        # Initialize DINO feature extractor with LoftUP
        print("\nInitializing DINO feature extractor with LoftUP...")
        
        extractor_loftup = DinoFeatureExtractor(
            device=device,
            backbone="dinov2",
            input_size=448,
            backbone_type="vit_small_reg",  # Use vit_small_reg as requested
            patch_size=14,
            use_loftup_sampler=True  # Enable LoftUP sampler
        )
        
        print(f"LoftUP extractor initialized:")
        print(f"  - Backbone: {extractor_loftup.backbone}")
        print(f"  - Backbone type: {extractor_loftup.backbone_type}")
        print(f"  - Input size: {extractor_loftup.input_size}")
        print(f"  - Feature dimension: {extractor_loftup.feature_dim}")
        print(f"  - Use LoftUP: {getattr(extractor_loftup, 'use_loftup_sampler', False)}")
        
        # Extract features without LoftUP
        print("\nExtracting features without LoftUP...")
        patch_feats_orig = []
        dense_feats_orig = []
        sparse_feats_orig = []
        edges_orig = []
        segs_orig = []
        centers_orig = []
        
        with torch.no_grad():
            for i, img_tensor in enumerate(img_tensors):
                print(f"Processing image {i+1}/3 (without LoftUP)...")
                img_batch_single = img_tensor.unsqueeze(0)  # Add batch dimension: [1, 3, H, W]
                patch_feat, dense_feat, sparse_feat, edges, seg, centers = extractor_original.extract_features_and_segments(img_batch_single)
                
                patch_feats_orig.append(patch_feat[0])  # Remove batch dimension
                dense_feats_orig.append(dense_feat[0])  # Remove batch dimension
                sparse_feats_orig.append(sparse_feat)
                edges_orig.append(edges)
                segs_orig.append(seg)
                centers_orig.append(centers)
        
        # Stack results back to batch format for compatibility
        patch_feat_orig = torch.stack(patch_feats_orig, dim=0)
        dense_feat_orig = torch.stack(dense_feats_orig, dim=0)
        
        print(f"Original results:")
        print(f"  - Patch features shape: {patch_feat_orig.shape}")
        print(f"  - Dense features shape: {dense_feat_orig.shape}")
        print(f"  - Number of segments per image: {[sf.shape[0] for sf in sparse_feats_orig]}")
        print(f"  - Segmentation shapes: {[seg.shape for seg in segs_orig]}")
        
        # Extract features with LoftUP
        print("\nExtracting features with LoftUP...")
        patch_feats_loftup = []
        dense_feats_loftup = []
        sparse_feats_loftup = []
        edges_loftup = []
        segs_loftup = []
        centers_loftup = []
        
        with torch.no_grad():
            for i, img_tensor in enumerate(img_tensors):
                print(f"Processing image {i+1}/3 (with LoftUP)...")
                img_batch_single = img_tensor.unsqueeze(0)  # Add batch dimension: [1, 3, H, W]
                patch_feat, dense_feat, sparse_feat, edges, seg, centers = extractor_loftup.extract_features_and_segments(img_batch_single)
                
                patch_feats_loftup.append(patch_feat[0])  # Remove batch dimension
                dense_feats_loftup.append(dense_feat[0])  # Remove batch dimension
                sparse_feats_loftup.append(sparse_feat)
                edges_loftup.append(edges)
                segs_loftup.append(seg)
                centers_loftup.append(centers)
        
        # Stack results back to batch format for compatibility
        patch_feat_loftup = torch.stack(patch_feats_loftup, dim=0)
        dense_feat_loftup = torch.stack(dense_feats_loftup, dim=0)
        
        print(f"LoftUP results:")
        print(f"  - Patch features shape: {patch_feat_loftup.shape}")
        print(f"  - Dense features shape: {dense_feat_loftup.shape}")
        print(f"  - Number of segments per image: {[sf.shape[0] for sf in sparse_feats_loftup]}")
        print(f"  - Segmentation shapes: {[seg.shape for seg in segs_loftup]}")
        
        # Compare results
        print("\n=== Feature Comparison ===")
        print(f"Original dense feature resolution: {dense_feat_orig.shape[2]}x{dense_feat_orig.shape[3]}")
        print(f"LoftUP dense feature resolution: {dense_feat_loftup.shape[2]}x{dense_feat_loftup.shape[3]}")
        
        resolution_improvement = (dense_feat_loftup.shape[2] * dense_feat_loftup.shape[3]) / (dense_feat_orig.shape[2] * dense_feat_orig.shape[3])
        print(f"Resolution improvement: {resolution_improvement:.2f}x")
        
        # Generate visualizations
        print("\nGenerating visualization comparisons...")
        visualize_loftup_comparison(
            original_imgs=original_imgs,
            patch_features=patch_feat_loftup.cpu(),
            dense_features_original=dense_feat_orig.cpu(),
            dense_features_loftup=dense_feat_loftup.cpu(),
            save_dir=str(output_dir)
        )
        
        # Save summary results
        results_text = f"""LoftUP Sampler Test Results
===============================

Configuration:
- Backbone: {extractor_loftup.backbone}
- Backbone type: {extractor_loftup.backbone_type}
- Input size: {extractor_loftup.input_size}
- Feature dimension: {extractor_loftup.feature_dim}
- Number of test images: {len(original_imgs)}

Feature Extraction Results:
- Patch features shape: {patch_feat_loftup.shape}
- Dense features shape (original): {dense_feat_orig.shape}
- Dense features shape (LoftUP): {dense_feat_loftup.shape}
- Sparse features shape: {sparse_feats_loftup}
- Segmentation shapes: {segs_loftup}

Performance Analysis:
- Original resolution: {dense_feat_orig.shape[2]}x{dense_feat_orig.shape[3]}
- LoftUP resolution: {dense_feat_loftup.shape[2]}x{dense_feat_loftup.shape[3]}
- Resolution improvement: {resolution_improvement:.2f}x

Results saved to: {output_dir}/
"""
        
        # Save results to file
        with open(output_dir / "loftup_test_results.txt", "w") as f:
            f.write(results_text)
        
        print(f"\nResults saved to {output_dir}/")
        print("LoftUP sampler test completed successfully!")
        
    except Exception as e:
        print(f"Error during LoftUP test: {e}")
        print("This might be due to missing model dependencies or network issues.")
        
    finally:
        # Clean up temporary files
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print("Cleaned up temporary files.")

if __name__ == "__main__":
    main()

