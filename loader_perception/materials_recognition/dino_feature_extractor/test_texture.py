import torch
import numpy as np
import matplotlib.pyplot as plt
from PIL import Image
import requests
from io import BytesIO
import torch.nn.functional as F
from sklearn.metrics.pairwise import cosine_similarity
import cv2
import os
from pathlib import Path
import shutil
import warnings
import gc  # 添加垃圾回收模块

# Import your DINO feature extractor
from dino_feature_extractor import DinoFeatureExtractor

warnings.filterwarnings('ignore')

def clear_gpu_memory():
    """清理GPU内存的辅助函数"""
    torch.cuda.empty_cache()
    gc.collect()
    if torch.cuda.is_available():
        print(f"GPU memory after cleanup: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")

def download_image(url, save_path):
    """Download image from URL and save locally."""
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        print(f"Downloaded: {save_path}")
        return True
    except Exception as e:
        print(f"Failed to download {url}: {e}")
        return False

def load_image_from_path(image_path):
    """Load image from local path and convert to PIL Image"""
    try:
        image = Image.open(image_path).convert('RGB')
        print(f"Loaded image: {image_path} - Size: {image.size}")
        return image
    except Exception as e:
        print(f"Failed to load {image_path}: {e}")
        return None

def preprocess_image(image, size=(224, 224)):  # 降低分辨率从448到224
    """Convert PIL image to tensor format expected by DINO"""
    image = image.resize(size)
    image_array = np.array(image) / 255.0
    image_tensor = torch.from_numpy(image_array).permute(2, 0, 1).float()
    return image_tensor.unsqueeze(0)  # Add batch dimension

def compute_cosine_similarity(feat1, feat2):
    """Compute cosine similarity between two feature vectors"""
    feat1_np = feat1.cpu().numpy().reshape(1, -1)
    feat2_np = feat2.cpu().numpy().reshape(1, -1)
    return cosine_similarity(feat1_np, feat2_np)[0, 0]

def save_segmentation_result(image, seg_mask, similarity_scores, output_path, threshold=0.7):
    """Save segmentation visualization with similarity-based coloring"""
    # Convert image tensor to numpy array
    if isinstance(image, torch.Tensor):
        if image.dim() == 4:  # Remove batch dimension
            image = image.squeeze(0)
        image_np = image.permute(1, 2, 0).cpu().numpy()
    else:
        image_np = np.array(image) / 255.0
    
    # Create colored mask based on similarity scores
    colored_mask = np.zeros_like(image_np)
    unique_segments = np.unique(seg_mask.cpu().numpy())
    
    # Ensure similarity_scores matches the number of unique segments
    print(f"Number of unique segments: {len(unique_segments)}")
    print(f"Number of similarity scores: {len(similarity_scores)}")
    
    for i, seg_id in enumerate(unique_segments):
        if i < len(similarity_scores):
            mask = (seg_mask.cpu().numpy() == seg_id)
            # Color based on similarity: red for high similarity, blue for low
            similarity = similarity_scores[i]
            if similarity > threshold:
                colored_mask[mask] = [1.0, 0, 0]  # Red for material regions
            else:
                colored_mask[mask] = [0, 0, similarity]  # Blue gradient for non-material
    
    # Create overlay
    overlay = 0.6 * image_np + 0.4 * colored_mask
    
    # Save the result
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(image_np)
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(1, 3, 2)
    plt.imshow(colored_mask)
    plt.title('Similarity-based Mask')
    plt.axis('off')
    
    plt.subplot(1, 3, 3)
    plt.imshow(overlay)
    plt.title('Overlay Result')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()

def save_slic_segmentation(image, seg_mask, output_path):
    """Save SLIC segmentation visualization"""
    # Convert image tensor to numpy array
    if isinstance(image, torch.Tensor):
        if image.dim() == 4:  # Remove batch dimension
            image = image.squeeze(0)
        image_np = image.permute(1, 2, 0).cpu().numpy()
    else:
        image_np = np.array(image) / 255.0
    
    # Convert segmentation mask to numpy
    seg_np = seg_mask.cpu().numpy()
    
    # Create colored segmentation mask
    unique_segments = np.unique(seg_np)
    colored_seg = np.zeros_like(image_np)
    
    # Generate different colors for each segment
    np.random.seed(42)  # For reproducible colors
    colors = np.random.rand(len(unique_segments), 3)
    
    for i, seg_id in enumerate(unique_segments):
        mask = (seg_np == seg_id)
        colored_seg[mask] = colors[i]
    
    # Create overlay with original image
    overlay = 0.7 * image_np + 0.3 * colored_seg
    
    # Save the result
    plt.figure(figsize=(15, 5))
    
    plt.subplot(1, 3, 1)
    plt.imshow(image_np)
    plt.title('Original Image')
    plt.axis('off')
    
    plt.subplot(1, 3, 2)
    plt.imshow(colored_seg)
    plt.title(f'SLIC Segmentation ({len(unique_segments)} segments)')
    plt.axis('off')
    
    plt.subplot(1, 3, 3)
    plt.imshow(overlay)
    plt.title('SLIC Overlay')
    plt.axis('off')
    
    plt.tight_layout()
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"SLIC segmentation saved to: {output_path}")

def main():
    # 在开始前清理GPU内存
    print("Checking GPU memory usage...")
    if torch.cuda.is_available():
        print(f"GPU memory before start: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
        clear_gpu_memory()
    
    # Create temporary directory for downloaded images
    temp_dir = Path("./temp_images")
    temp_dir.mkdir(exist_ok=True)
    
    # Initialize DINO feature extractor with optimized parameters
    print("Initializing DINO feature extractor...")
    extractor = DinoFeatureExtractor(
        device="cuda" if torch.cuda.is_available() else "cpu",
        backbone="dinov2",
        input_size=224,  # 降低输入分辨率从448到224
        backbone_type="vit_small_reg",
        patch_size=14,
        slic_n_segments=400,
        slic_compactness=30.0,
        use_loftup_sampler=True
    )
    
    # 检查模型加载后的内存使用
    if torch.cuda.is_available():
        print(f"GPU memory after model loading: {torch.cuda.memory_allocated() / 1024**3:.2f} GB")
    
    # Image URLs
    texture_urls = [
        "https://ml-img.oss-cn-beijing.aliyuncs.com/texture1.jpg",
        "https://ml-img.oss-cn-beijing.aliyuncs.com/texture0.jpg"
    ]
    
    non_texture_urls = [
        "https://ml-img.oss-cn-beijing.aliyuncs.com/non_texture0.jpg",
        "https://ml-img.oss-cn-beijing.aliyuncs.com/non_texture1.jpg"
    ]
    
    test_image_url = "https://ml-img.oss-cn-beijing.aliyuncs.com/test_texture.jpg"
    
    # Create debug output directory
    output_dir = Path("~/loader_ws/src/debug/texture_analysis").expanduser()
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        print("Downloading and processing images...")
        
        # Download texture images
        texture_paths = []
        for i, url in enumerate(texture_urls):
            filename = f"texture_{i+1}.jpg"
            save_path = temp_dir / filename
            if download_image(url, save_path):
                texture_paths.append(save_path)
            else:
                print(f"Skipping texture image {i+1} due to download failure")
        
        # Download non-texture images
        non_texture_paths = []
        for i, url in enumerate(non_texture_urls):
            filename = f"non_texture_{i+1}.jpg"
            save_path = temp_dir / filename
            if download_image(url, save_path):
                non_texture_paths.append(save_path)
            else:
                print(f"Skipping non-texture image {i+1} due to download failure")
        
        # Download test image
        test_image_path = temp_dir / "test_image.jpg"
        test_downloaded = download_image(test_image_url, test_image_path)
        
        if not texture_paths or not non_texture_paths or not test_downloaded:
            print("Failed to download required images. Exiting.")
            return
        
        # Load images from downloaded files
        texture_images = []
        for path in texture_paths:
            img = load_image_from_path(path)
            if img:
                texture_images.append(img)
        
        non_texture_images = []
        for path in non_texture_paths:
            img = load_image_from_path(path)
            if img:
                non_texture_images.append(img)
        
        test_image = load_image_from_path(test_image_path)
        
        if not texture_images or not non_texture_images or not test_image:
            print("Failed to load required images. Exiting.")
            return
        
        print("Extracting features...")
        
        # 逐个处理图像以减少内存使用
        texture_features = []
        texture_results = []
        
        for i, img in enumerate(texture_images):
            print(f"Processing texture image {i+1}...")
            tensor = preprocess_image(img)
            
            # 移动到GPU并处理
            if torch.cuda.is_available():
                tensor = tensor.cuda()
            
            with torch.no_grad():  # 禁用梯度计算以节省内存
                patch_feat, dense_feat, sparse_feat, edges, seg, centers = extractor.extract_features_and_segments(tensor)
                # 移动到CPU以释放GPU内存
                patch_feat = patch_feat.cpu()
                sparse_feat = sparse_feat.cpu()
                seg = seg.cpu()
                
                texture_features.append(patch_feat)
                texture_results.append((patch_feat, dense_feat, sparse_feat, edges, seg, centers))
                print(f"Texture image {i+1}: Patch features shape: {patch_feat.shape}")
            
            # 清理临时变量
            del tensor
            clear_gpu_memory()
        
        non_texture_features = []
        for i, img in enumerate(non_texture_images):
            print(f"Processing non-texture image {i+1}...")
            tensor = preprocess_image(img)
            
            if torch.cuda.is_available():
                tensor = tensor.cuda()
            
            with torch.no_grad():
                patch_feat, dense_feat, sparse_feat, edges, seg, centers = extractor.extract_features_and_segments(tensor)
                patch_feat = patch_feat.cpu()
                
                non_texture_features.append(patch_feat)
                print(f"Non-texture image {i+1}: Patch features shape: {patch_feat.shape}")
            
            # 清理临时变量
            del tensor
            clear_gpu_memory()
        
        # Extract features for test image
        print("Processing test image...")
        test_tensor = preprocess_image(test_image)
        
        if torch.cuda.is_available():
            test_tensor = test_tensor.cuda()
        
        with torch.no_grad():
            test_patch_feat, test_dense_feat, test_sparse_feat, test_edges, test_seg, test_centers = extractor.extract_features_and_segments(test_tensor)
            # 移动关键结果到CPU
            test_patch_feat = test_patch_feat.cpu()
            test_sparse_feat = test_sparse_feat.cpu()
            test_seg = test_seg.cpu()
            test_tensor = test_tensor.cpu()  # 用于后续可视化
            
        print(f"Test image: Patch features shape: {test_patch_feat.shape}")
        print(f"Test image: Number of segments: {test_sparse_feat.shape[0]}")
        
        # 清理GPU内存
        clear_gpu_memory()
        
        # 1. Calculate average pooled patch features
        print("\n=== Feature Similarity Analysis ===")
        
        # Average pool patch features
        texture_pooled = [feat.mean(dim=1) for feat in texture_features]  # [B, C]
        non_texture_pooled = [feat.mean(dim=1) for feat in non_texture_features]
        
        # 2. Similarity between two material texture images
        sim_texture_texture = compute_cosine_similarity(texture_pooled[0], texture_pooled[1])
        print(f"Similarity between two material texture images: {sim_texture_texture:.4f}")
        
        # 3. Similarity between material and non-material texture images
        print("\nSimilarity between material and non-material textures:")
        for i, tex_feat in enumerate(texture_pooled):
            for j, non_tex_feat in enumerate(non_texture_pooled):
                sim = compute_cosine_similarity(tex_feat, non_tex_feat)
                print(f"  Material {i+1} vs Non-material {j+1}: {sim:.4f}")
        
        # 4. Similarity between pooled features and individual patch features
        print("\nSimilarity between pooled features and individual patches:")
        for i, (tex_feat, tex_pooled_feat) in enumerate(zip(texture_features, texture_pooled)):
            # Calculate similarity between pooled feature and each patch
            patch_similarities = []
            for patch_idx in range(tex_feat.shape[1]):  # Loop through patches
                patch_feat = tex_feat[0, patch_idx, :]  # [C]
                sim = compute_cosine_similarity(tex_pooled_feat, patch_feat.unsqueeze(0))
                patch_similarities.append(sim)
            
            avg_sim = np.mean(patch_similarities)
            max_sim = np.max(patch_similarities)
            min_sim = np.min(patch_similarities)
            print(f"  Material {i+1} - Avg: {avg_sim:.4f}, Max: {max_sim:.4f}, Min: {min_sim:.4f}")
        
        # 5. Segmentation based on similarity
        print("\n=== Segmentation Analysis ===")
        
        # Use average of material texture features as reference
        reference_feature = torch.mean(torch.stack(texture_pooled), dim=0)  # [B, C]
        print(f"Reference feature shape: {reference_feature.shape}")
        print(f"Test sparse features shape: {test_sparse_feat.shape}")
        
        # Calculate similarity between reference and each segment
        segment_similarities = []
        for seg_idx in range(test_sparse_feat.shape[0]):
            seg_feat = test_sparse_feat[seg_idx, :].unsqueeze(0)  # [1, C]
            sim = compute_cosine_similarity(reference_feature, seg_feat)
            segment_similarities.append(sim)
        
        segment_similarities = np.array(segment_similarities)
        
        print(f"Segment similarities - Mean: {segment_similarities.mean():.4f}")
        print(f"                      Std:  {segment_similarities.std():.4f}")
        print(f"                      Max:  {segment_similarities.max():.4f}")
        print(f"                      Min:  {segment_similarities.min():.4f}")
        
        # Find segments with high similarity (potential material regions)
        # threshold = segment_similarities.mean() + segment_similarities.std()
        threshold = segment_similarities.mean()

        material_segments = segment_similarities > threshold
        print(f"Threshold: {threshold:.4f}")
        print(f"Number of material segments: {material_segments.sum()}")
        print(f"Total segments: {len(segment_similarities)}")
        
        # Save segmentation results
        print("\nSaving results...")
        
        # Save SLIC segmentation visualization
        save_slic_segmentation(
            test_tensor,
            test_seg,
            str(output_dir / "slic_segmentation.jpg")
        )
        
        # Save segmentation visualization with similarity-based coloring
        save_segmentation_result(
            test_tensor, 
            test_seg, 
            segment_similarities, 
            str(output_dir / "segmentation_result.jpg"),
            threshold=threshold
        )
        
        # Save similarity scores
        results_text = f"""DINO Feature Analysis Results
=====================================

Image Information:
- Texture images: {len(texture_images)}
- Non-texture images: {len(non_texture_images)}
- Test image segments: {test_sparse_feat.shape[0]}
- Input resolution: 224x224 (optimized for memory)

Similarity Analysis:
- Material texture 1 vs Material texture 2: {sim_texture_texture:.4f}

Material vs Non-material similarities:
"""
        
        for i, tex_feat in enumerate(texture_pooled):
            for j, non_tex_feat in enumerate(non_texture_pooled):
                sim = compute_cosine_similarity(tex_feat, non_tex_feat)
                results_text += f"- Material {i+1} vs Non-material {j+1}: {sim:.4f}\n"
        
        results_text += f"""
Segmentation Results:
- Similarity threshold: {threshold:.4f}
- Material segments detected: {material_segments.sum()}/{len(segment_similarities)}
- Segment similarities: Mean={segment_similarities.mean():.4f}, Std={segment_similarities.std():.4f}

Top 10 most similar segments:
"""
        
        # Sort segments by similarity
        sorted_indices = np.argsort(segment_similarities)[::-1]
        for i in range(min(10, len(sorted_indices))):
            idx = sorted_indices[i]
            results_text += f"- Segment {idx}: {segment_similarities[idx]:.4f}\n"
        
        # Save results to file
        with open(output_dir / "analysis_results.txt", "w") as f:
            f.write(results_text)
        
        print(f"Results saved to {output_dir}/")
        print("Analysis complete!")
        
        # 最终清理
        clear_gpu_memory()
        
    except Exception as e:
        print(f"Error during texture analysis: {e}")
        print("This might be due to missing model dependencies or network issues.")
        # 发生错误时也要清理内存
        clear_gpu_memory()
        
    finally:
        # Clean up temporary files
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
            print("Cleaned up temporary files.")

if __name__ == "__main__":
    main()