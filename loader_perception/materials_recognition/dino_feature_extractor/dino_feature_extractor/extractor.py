import torch
import warnings
import numpy as np
from omegaconf import OmegaConf
from .models import Dinov2ViT, DinoViT
from .segment_graph_extractor import SegmentGraphExtractor
from torchvision import transforms as T
from skimage.segmentation import slic
import kornia
import torch.nn.functional as F

warnings.filterwarnings('ignore')

class DinoFeatureExtractor:
    """Unified DINO feature extractor for traversability learning.

    This class combines DINO/DINOv2 model interface and feature extraction functionality.
    It provides a complete solution for loading DINO models, preprocessing images,
    and extracting features for traversability analysis.

    Note:
        - DINO models will automatically download pretrained weights from official repository
        - DINOv2 models use torch.hub for automatic weight management
        - Weights are cached at: ~/.cache/torch/hub/checkpoints/ (DINO) and ~/.cache/torch/hub/ (DINOv2)
    """

    def __init__(self,
                 device: str = "cuda",
                 backbone: str = "dinov2",
                 input_size: int = 448,
                 backbone_type: str = "vit_base_reg",
                 patch_size: int = 14,
                 projection_type: str = None,  # nonlinear or None
                 dropout_p: float = 0,  # dropout probability
                 slic_n_segments: int = 400,  # number of SLIC segments
                 slic_compactness: float = 30.0,  # SLIC compactness parameter
                 use_loftup_sampler: bool = True,
                 cfg: OmegaConf = None):
        """Initialize unified DINO feature extractor.

        Args:
            device (str): Device to run the model on ('cuda' or 'cpu'), defaults to "cuda"
            backbone (str): Type of backbone network ('dino' or 'dinov2'), defaults to "dinov2"
            input_size (int): Input image size, defaults to 448
            backbone_type (str): Specific type of ViT backbone (e.g. 'vit_base_reg'), defaults to "vit_base_reg"
            patch_size (int): Size of ViT patches, defaults to 14
            projection_type (str): Type of projection head (None or 'nonlinear'), defaults to None
            dropout_p (float): Dropout probability, defaults to 0
            slic_n_segments (int): Number of SLIC segments, defaults to 400
            slic_compactness (float): SLIC compactness parameter, defaults to 30.0
            cfg (OmegaConf): Configuration object, defaults to None
        """
        # Load or create configuration
        if cfg is None or OmegaConf.is_empty(cfg):
            self._cfg = OmegaConf.create({
                "backbone": backbone,
                "backbone_type": backbone_type,
                "input_size": input_size,
                "patch_size": patch_size,
                "projection_type": projection_type,
                "dropout_p": dropout_p,
            })
        else:
            self._cfg = cfg

        if device == "cuda" and not torch.cuda.is_available():
            print("CUDA not available, falling back to CPU")
            device = "cpu"

        # Initialize DINO model based on configuration
        self._model = self._get_backbone(self._cfg)

        # Move model to specified device
        self._model.to(device)
        self._device = device

        # Get feature dimension from model
        self._feature_dim = self._model.get_output_feat_dim()

        # Setup image preprocessing pipeline
        # Use ImageNet normalization parameters
        normalization = T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225])
        self._transform = T.Compose([
            T.Resize(input_size, T.InterpolationMode.NEAREST),
            T.CenterCrop(input_size),
            normalization,
        ])
        self._transform_slic = T.Compose([
            T.Resize(input_size, T.InterpolationMode.NEAREST),
            T.CenterCrop(input_size),
        ])

        # Store SLIC parameters for direct usage
        self.slic_n_segments = slic_n_segments
        self.slic_compactness = slic_compactness

        # loftup sampler
        if use_loftup_sampler:
            self.use_loftup_sampler = use_loftup_sampler
            # dinov2_class "vit_base_reg", "vit_base", "vit_small_reg", "vit_small"
            # torch_hub_name = "loftup_dinov2b_reg", "loftup_dinov2b", "loftup_dinov2s_reg", "loftup_dinov2s"
            # select the corresponding loftup model based on the backbone type
            backbone_type = self._cfg.backbone_type
            loftup_model_name = {
                "vit_base_reg": "loftup_dinov2b_reg",
                "vit_base": "loftup_dinov2b", 
                "vit_small_reg": "loftup_dinov2s_reg",
                "vit_small": "loftup_dinov2s"
            }.get(backbone_type, "loftup_dinov2s")  # default to use small model

            # load and move loftup model to the specified device
            # set eval mode
            self.loftup_sampler = torch.hub.load('andrehuang/loftup', loftup_model_name, pretrained=True)
            self.loftup_sampler.eval()
            self.loftup_sampler = self.loftup_sampler.to(device)
        else:
            self.use_loftup_sampler = False

        # Initialize segment graph extractor for adjacency list and centers computation
        self.segment_extractor = SegmentGraphExtractor()
        # Move segment extractor to the same device as the main model
        self.segment_extractor.to(device)

    def _get_backbone(self, cfg):
        """Get the appropriate backbone model based on configuration.

        Args:
            cfg (OmegaConf): Configuration object

        Returns:
            nn.Module: Initialized backbone model

        Raises:
            ValueError: If backbone type is not found or invalid
        """
        if not hasattr(cfg, "backbone"):
            raise ValueError("Could not find 'backbone' option in the config file. Please check it")

        if cfg.backbone == "dino":
            return DinoViT(cfg)
        elif cfg.backbone == "dinov2":
            return Dinov2ViT(cfg)
        else:
            raise ValueError("Backbone {} unavailable".format(cfg.backbone))
    @torch.no_grad()
    def extract_features_and_segments(self, img_tensors, **kwargs):
        """Extract image features and segments for traversability analysis.

        This method combines feature extraction and image segmentation,
        providing a complete solution for graph-based traversability learning.

        Args:
            img_tensors (torch.Tensor): Input image tensor of shape (B, 3, H, W)
            **kwargs: Additional keyword arguments for segmentation methods

        Returns:
            tuple: A tuple containing:
                - features (torch.Tensor): Feature tensor of shape [B, num_patches, C], where B is batch size,
                                         num_patches is number of patches, and C is feature dimension
                - dense_features (torch.Tensor): Dense feature tensor of shape (B, C, H, W) after interpolation to match original image size
                - sparse_features (torch.Tensor): Sparse feature tensor of shape (num_segments, C) aggregated by segments
                - edges (torch.Tensor): Adjacency list of shape (2, num_edges) representing graph edges
                - seg (torch.Tensor): Segmentation map of shape (H, W) with segment IDs
                - centers (torch.Tensor): Segment centers of shape (num_segments, 2) in image coordinates
        """
        # Get original image dimensions
        B, D, H, W = img_tensors.shape
        
        # Extract DINO features
        dino_features, norm_img = self._compute_dino_features(img_tensors)  # [B, H_feat, W_feat, C], normalized_img
        B_feat, H_feat, W_feat, C = dino_features.shape
        
        # Convert to [B, C, H_feat, W_feat] for interpolation
        dense_features = dino_features.permute(0, 3, 1, 2)  # [B, C, H_feat, W_feat]
        
        # Compute segmentation and graph structure first to ensure consistency
        edges, seg, centers = self.compute_segments(img_tensors, **kwargs)
        
        # Get the actual segmentation dimensions
        seg_H, seg_W = seg.shape
        
        # Interpolate features to match EXACTLY the segmentation dimensions
        # This ensures perfect alignment between features and segmentation
        if self.use_loftup_sampler:
            # use loftup sampler to interpolate features
            dense_features = self.loftup_sampler(dense_features, norm_img)
        else:
            dense_features = F.interpolate(
                dense_features, 
                size=(seg_H, seg_W), 
                mode="bilinear", 
                align_corners=True
            )  # [B, C, seg_H, seg_W]
        
        # print(f"Original image size: ({H}, {W})")
        # print(f"DINO feature size: ({H_feat}, {W_feat})")
        # print(f"Segmentation size: ({seg_H}, {seg_W})")
        # print(f"Interpolated feature size: {dense_features.shape[2:]}")
        
        # Sparsify features based on segmentation
        sparse_features = self.sparsify_features(dense_features, seg)
        
        # Also prepare standard features format for backward compatibility
        patch_features = dino_features.view(B_feat, H_feat * W_feat, C)  # [B, num_patches, C]
        
        return patch_features, dense_features, sparse_features, edges, seg, centers
    @torch.no_grad()
    def extract_features(self, img_tensors):
        """Extract image features for traversability analysis.

        Args:
            img_tensors (torch.Tensor): Input image tensor of shape (B, 3, H, W)

        Returns:
            torch.Tensor: Feature tensor of shape [B, num_patches, C], where B is batch size,
                         num_patches is number of patches, and C is feature dimension
        """
        features, _ = self._compute_dino_features(img_tensors)  # [B, H, W, C]
        B, H, W, C = features.shape
        features = features.view(B, H * W, C)  # [B, num_patches, C]
        return features

    @torch.no_grad()
    def _compute_dino_features(self, img: torch.tensor):
        """Internal method to compute DINO features with preprocessing.

        Args:
            img (torch.Tensor): Input image tensor of shape (B, 3, H, W)

        Returns:
            torch.Tensor: Feature tensor of shape (B, H, W, C) - per-pixel features
        """
        # Clone input to avoid modifying original tensor
        img_internal = img.clone()

        # Apply preprocessing transforms to each image in the batch
        batch_size = img_internal.shape[0]
        processed_imgs = []

        for i in range(batch_size):
            # Extract single image (C, H, W)
            single_img = img_internal[i]
            # Apply transforms to single image
            processed_img = self._transform(single_img)
            processed_imgs.append(processed_img)

        # Stack processed images back to batch format (B, C, input_size, input_size)
        resized_img = torch.stack(processed_imgs, dim=0).to(self._device)

        # Extract features using the backbone model
        features = self._model(resized_img)
        return features, resized_img
    
    @torch.no_grad()
    def compute_segments(self, img: torch.tensor, **kwargs):
        """Compute SLIC image segments and extract graph structure.

        This method performs SLIC (Simple Linear Iterative Clustering) segmentation
        and extracts graph structure including edges (adjacency list) and segment centers
        for graph-based processing.

        Args:
            img (torch.Tensor): Input image tensor of shape (B, 3, H, W)
            **kwargs: Additional keyword arguments for segmentation methods

        Returns:
            tuple: A tuple containing:
                - edges (torch.Tensor): Adjacency list of shape (2, num_edges) representing graph edges
                - seg (torch.Tensor): Segmentation map of shape (H, W) with segment IDs
                - centers (torch.Tensor): Segment centers of shape (num_segments, 2) in image coordinates
        """
        # Perform SLIC segmentation
        # resize the image to the input size
        img = self._transform_slic(img)
        seg = self.segment_slic(img, **kwargs)

        # Extract adjacency_list based on segments
        # This creates a graph where nodes are segments and edges connect adjacent segments
        edges = self.segment_extractor.adjacency_list(seg)

        # Extract centers of each segment in image coordinates
        centers = self.segment_extractor.centers(seg)

        # Return edges transposed to (2, num_edges), segmentation map, and centers
        return edges.T, seg[0, 0], centers
    
    @torch.no_grad()
    def segment_slic(self, img: torch.tensor, **kwargs):
        """Perform SLIC (Simple Linear Iterative Clustering) segmentation using scikit-image.

        SLIC is a superpixel segmentation algorithm that groups pixels into
        perceptually meaningful atomic regions. It combines spatial proximity
        and color similarity in a unified distance measure.

        Args:
            img (torch.Tensor): Input image tensor of shape (B, 3, H, W)
            **kwargs: Additional keyword arguments (currently unused)

        Returns:
            torch.Tensor: Segmentation map of shape (1, 1, H, W) with segment IDs as long tensor
                         Each pixel value represents the ID of the segment it belongs to
        """
        # Extract single image from batch (assume batch size = 1)
        if img.shape[0] != 1:
            raise ValueError("SLIC segmentation currently supports batch size of 1 only")

        # Convert PyTorch tensor to numpy image format using kornia utility
        # kornia.utils.tensor_to_image converts (B, C, H, W) or (C, H, W) to (H, W, C)
        img_np = kornia.utils.tensor_to_image(img)

        # Convert to uint8 format expected by SLIC algorithms
        img_uint8 = np.uint8(np.ascontiguousarray(img_np) * 255)

        # Use scikit-image SLIC for reliable and high-quality superpixel segmentation
        seg_result = slic(
            img_uint8,
            n_segments=self.slic_n_segments,
            compactness=self.slic_compactness,
            sigma=1,
            start_label=0
        )

        # Convert back to PyTorch tensor with proper shape and data type
        # Add batch and channel dimensions: (H, W) -> (1, 1, H, W)
        seg_tensor = torch.from_numpy(seg_result)[None, None]

        # Move to the same device as the model and convert to long tensor for indexing
        return seg_tensor.to(self._device).type(torch.long)

    @torch.no_grad()
    def sparsify_features(self, dense_features: torch.Tensor, seg: torch.Tensor):
        """Sparsify dense features based on segmentation mask.
        
        This method aggregates dense features within each segment by computing
        the mean feature vector for all pixels belonging to the same segment.
        
        Args:
            dense_features (torch.Tensor): Dense feature tensor of shape (B, C, H, W)
            seg (torch.Tensor): Segmentation map of shape (H, W) with segment IDs
            
        Returns:
            torch.Tensor: Sparse feature tensor of shape (num_segments, C) where each row
                         contains the mean feature vector for the corresponding segment
        """
        # Extract single batch (assume batch size = 1)
        if dense_features.shape[0] != 1:
            raise ValueError("Sparsify features currently supports batch size of 1 only")
            
        dense_features = dense_features[0]  # Shape: (C, H, W)
        
        # Initialize list to store sparse features for each segment
        sparse_features = []
        
        # Iterate through each segment ID
        num_segments = seg.max() + 1
        for i in range(num_segments):
            # Create mask for current segment
            mask = seg == i  # Shape: (H, W)
            
            # Find pixel coordinates belonging to this segment
            x, y = torch.where(mask)
            
            if len(x) > 0:
                # Extract features for pixels in this segment and compute mean
                segment_features = dense_features[:, x, y]  # Shape: (C, num_pixels_in_segment)
                mean_feature = segment_features.mean(dim=1)  # Shape: (C,)
            else:
                # Handle empty segments (shouldn't happen in practice)
                mean_feature = torch.zeros(dense_features.shape[0], device=dense_features.device)
                
            sparse_features.append(mean_feature)
        
        # Stack all segment features
        sparse_features = torch.stack(sparse_features, dim=0)  # Shape: (num_segments, C)
        
        return sparse_features

    @property
    def feature_dim(self):
        """Get feature dimension.

        Returns:
            int: Feature dimension
        """
        return self._feature_dim

    @property
    def input_size(self):
        """Get input image size.

        Returns:
            int: Input image size
        """
        return self._cfg.input_size

    @property
    def backbone(self):
        """Get backbone type.

        Returns:
            str: Backbone type ('dino' or 'dinov2')
        """
        return self._cfg.backbone

    @property
    def backbone_type(self):
        """Get specific ViT backbone type.

        Returns:
            str: ViT backbone type (e.g. 'vit_base_reg')
        """
        return self._cfg.backbone_type

    @property
    def vit_patch_size(self):
        """Get ViT patch size.

        Returns:
            int: Patch size
        """
        return self._cfg.patch_size

    def change_device(self, device):
        """Change model running device.

        Args:
            device (str): New device name ('cuda' or 'cpu')
        """
        self._device = device
        self._model.to(device)
        # Also move segment extractor to the new device
        self.segment_extractor.to(device)