# DINO Feature Extractor Node Configuration
# This file contains all configurable parameters for the DINO feature extractor node

dino_feature_extractor_node:
  ros__parameters:
    # Camera topics configuration
    image_topic: "/camera_sensor/image_raw"              # RGB camera image topic
    camera_info_topic: "/camera_sensor/depth/camera_info"     # Camera info topic
    features_topic: "/dino_feature_extractor_node/image_features"            # Output features topic
    reference_features_topic: "/dino_feature_extractor_node/reference_features"    # Output reference features topic  
    segmentation_topic: "/dino_feature_extractor_node/image_segmentation"            # Output segmentation topic
    
    # DINO model configuration - 使用更安全的参数以避免CUDA索引越界
    device: "cuda"                               # Device: "cuda" or "cpu"
    backbone: "dinov2"                          # Backbone type: "dino" or "dinov2"
    backbone_type: "vit_base_reg"              # ViT type: "vit_base_reg", "vit_large_reg", etc.
    input_size: 448                            # Input image size (square)
    patch_size: 14                             # ViT patch size
    projection_type: ""                        # Projection type: "" (None) or "nonlinear"
    dropout_p: 0.0                            # Dropout probability
    use_loftup_sampler: true                  # Enable LoftUp sampler for feature interpolation
    
    # SLIC segmentation parameters - 使用更保守的值
    slic_n_segments: 400                      # Number of SLIC segments (reduced from 400)
    slic_compactness: 30.0                    # SLIC compactness parameter (reduced from 30.0)
    
    # Reference texture image URLs
    reference_texture_urls: 
      - "https://ml-img.oss-cn-beijing.aliyuncs.com/texture1.jpg"
      - "https://ml-img.oss-cn-beijing.aliyuncs.com/texture0.jpg"

# Alternative configurations for different scenarios:

# High-performance configuration (larger model, higher accuracy) - 谨慎使用
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cuda"
#     backbone: "dinov2"
#     backbone_type: "vit_large_reg"
#     input_size: 336                         # 降低到336而不是518
#     patch_size: 14
#     projection_type: "nonlinear"
#     dropout_p: 0.1
#     use_loftup_sampler: false               # 禁用LoftUp以减少复杂性
#     slic_n_segments: 200                    # 减少segment数量
#     slic_compactness: 25.0

# CPU-friendly configuration (smaller model, faster inference)
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cpu"
#     backbone: "dinov2"
#     backbone_type: "vit_small_reg"
#     input_size: 224
#     patch_size: 14
#     projection_type: ""
#     dropout_p: 0.0
#     use_loftup_sampler: false
#     slic_n_segments: 100
#     slic_compactness: 20.0

# Debug configuration (minimal resources for troubleshooting)
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cuda"
#     backbone: "dinov2"
#     backbone_type: "vit_small_reg"
#     input_size: 224
#     patch_size: 14
#     projection_type: ""
#     dropout_p: 0.0
#     use_loftup_sampler: false
#     slic_n_segments: 50                     # 非常少的segments用于调试
#     slic_compactness: 15.0

# Original DINO configuration
# dino_feature_extractor_node:
#   ros__parameters:
#     device: "cuda"
#     backbone: "dino"
#     backbone_type: "vit_base"
#     input_size: 224                         # 保持较小尺寸
#     patch_size: 16                          # DINO使用16x16 patches
#     projection_type: ""
#     dropout_p: 0.0
#     use_loftup_sampler: false
#     slic_n_segments: 100
#     slic_compactness: 20.0 