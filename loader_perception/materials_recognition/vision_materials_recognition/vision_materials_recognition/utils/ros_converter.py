import cv2
from geometry_msgs.msg import Pose
from nav_msgs.msg import Odometry
from sensor_msgs.msg import Image, CompressedImage
from cv_bridge import CvBridge

from liegroups.torch import SO3, SE3
import numpy as np
import torch
import torchvision.transforms as transforms
from pytictac import Timer
from PIL import Image as PILImage
from io import BytesIO
import requests

CV_BRIDGE = CvBridge()
TO_TENSOR = transforms.ToTensor()
TO_PIL_IMAGE = transforms.ToPILImage()
BASE_DIM = 7 + 6  # pose + twist

def ros_cam_info_to_tensors(caminfo_msg, device="cpu"):
    K = torch.eye(4, dtype=torch.float32).to(device)
    K[:3, :3] = torch.FloatTensor(caminfo_msg.k).reshape(3, 3)
    K = K.unsqueeze(0)
    H = caminfo_msg.height  # torch.IntTensor([caminfo_msg.height]).to(device)
    W = caminfo_msg.width   # torch.IntTensor([caminfo_msg.width]).to(device)
    return K, H, W

def ros_image_to_torch(ros_img, desired_encoding="rgb8", device="cpu"):
    if type(ros_img).__name__ == "_sensor_msgs__Image" or isinstance(ros_img, Image):
        np_image = CV_BRIDGE.imgmsg_to_cv2(ros_img, desired_encoding=desired_encoding)

    elif type(ros_img).__name__ == "_sensor_msgs__CompressedImage" or isinstance(ros_img, CompressedImage):
        np_arr = np.fromstring(ros_img.data, np.uint8)
        np_image = cv2.imdecode(np_arr, cv2.IMREAD_COLOR)
        if "bgr" in ros_img.format:
            np_image = cv2.cvtColor(np_image, cv2.COLOR_BGR2RGB)

    else:
        raise ValueError("Image message type is not implemented.")
        
    return TO_TENSOR(np_image).to(device)

def download_image_from_url(url: str, timeout: int = 10) -> PILImage.Image:
    """
    Download image from URL and convert to PIL Image.
    
    Args:
        url (str): URL of the image to download
        timeout (int): Timeout in seconds for the download request
        
    Returns:
        PILImage.Image: Downloaded and converted PIL image, None if failed
    """
    try:
        response = requests.get(url, timeout=timeout)
        response.raise_for_status()
        image = PILImage.open(BytesIO(response.content)).convert('RGB')
        return image
    except Exception as e:
        print(f"Failed to download image from {url}: {str(e)}")
        return None

def pil_image_to_torch_tensor(image: PILImage.Image, device: str = "cpu") -> torch.Tensor:
    """
    Convert PIL image to PyTorch tensor with minimal preprocessing.
    This function only handles basic format conversion and does NOT include 
    resizing, normalization, or other preprocessing that should be handled 
    by the DinoFeatureExtractor internally.
    
    Args:
        image (PILImage.Image): Input PIL image
        device (str): PyTorch device to place the tensor on
        
    Returns:
        torch.Tensor: Image tensor with shape [1, 3, H, W] (with batch dimension)
    """
    # Convert PIL image to tensor [3, H, W] with values in [0, 1]
    image_tensor = TO_TENSOR(image).to(device)
    
    # Add batch dimension: [3, H, W] -> [1, 3, H, W]
    image_tensor = image_tensor.unsqueeze(0)
    
    return image_tensor

def preprocess_image_for_dino(image: PILImage.Image, device: str = "cpu") -> torch.Tensor:
    """
    Preprocess PIL image for DINO feature extraction with minimal operations.
    
    Note: This function performs minimal preprocessing because DinoFeatureExtractor
    already includes comprehensive preprocessing (resizing, normalization, etc.)
    in its internal _compute_dino_features method.
    
    Args:
        image (PILImage.Image): Input PIL image  
        device (str): PyTorch device to place the tensor on
        
    Returns:
        torch.Tensor: Preprocessed image tensor with shape [1, 3, H, W]
    """
    return pil_image_to_torch_tensor(image, device)

def ros_mask_to_torch(ros_mask, threshold=128, device="cpu"):
    """
    Convert ROS image message containing a binary mask to PyTorch tensor.
    
    Args:
        ros_mask: ROS Image or CompressedImage message containing a binary mask
        threshold: Pixel value threshold to binarize the mask (default: 128)
        device: PyTorch device to place the tensor on (default: "cpu")
        
    Returns:
        Binary mask as PyTorch tensor with values 0 and 1
    """
    if type(ros_mask).__name__ == "_sensor_msgs__Image" or isinstance(ros_mask, Image):
        # Convert ROS image message to OpenCV image
        np_mask = CV_BRIDGE.imgmsg_to_cv2(ros_mask, desired_encoding='mono8')
        
    elif type(ros_mask).__name__ == "_sensor_msgs__CompressedImage" or isinstance(ros_mask, CompressedImage):
        # Decompress and convert to OpenCV image
        np_arr = np.fromstring(ros_mask.data, np.uint8)
        np_mask = cv2.imdecode(np_arr, cv2.IMREAD_GRAYSCALE)
        
    else:
        raise ValueError("Image message type is not implemented.")
    
    # Binarize the mask using the threshold
    binary_mask = (np_mask > threshold).astype(np.uint8)
    
    # Convert to PyTorch tensor and move to specified device
    return torch.from_numpy(binary_mask).to(device)

def torch_to_ros_image(torch_img, desired_encoding="rgb8"):
    """

    Args:
        torch_img (torch.tensor, shape=(C,H,W)): Image to convert to ROS message
        desired_encoding (str, optional): _description_. Defaults to "rgb8".

    Returns:
        _type_: _description_
    """

    np_img = np.array(TO_PIL_IMAGE(torch_img.cpu()))
    ros_img = CV_BRIDGE.cv2_to_imgmsg(np_img, encoding=desired_encoding)
    return ros_img

def numpy_to_ros_image(np_img, desired_encoding="rgb8"):
    """

    Args:
        np_img (np.array): Image to convert to ROS message
        desired_encoding (str, optional): _description_. Defaults to "rgb8".

    Returns:
        _type_: _description_
    """
    ros_image = CV_BRIDGE.cv2_to_imgmsg(np_img, encoding=desired_encoding)
    return ros_image

def ros_tf_to_torch(tf_pose, device="cpu"):
    assert len(tf_pose) == 2
    assert isinstance(tf_pose, tuple)
    if tf_pose[0] is None:
        return False, None
    t = torch.FloatTensor(tf_pose[0])
    q = torch.FloatTensor(tf_pose[1])
    return True, SE3(SO3.from_quaternion(q, ordering="xyzw"), t).as_matrix().to(device)

def ros_pose_to_tensor(pose_stamped, device):
    """
    将PoseStamped类型转换为符合 (B, N, 3) 格式的PyTorch张量，并设置设备。

    参数:
        pose_stamped: 包含3D位置信息的PoseStamped对象，必须有属性pose.position.x, .y, .z。
        device: 目标设备，例如 "cpu" 或 "cuda:0"。

    返回:
        points: torch.Tensor，形状为 (1, 1, 3)，数据类型为 torch.float32，并设置到指定设备上。
    """
    # 提取3D位置坐标
    x = pose_stamped.pose.position.x
    y = pose_stamped.pose.position.y
    z = pose_stamped.pose.position.z

    # 构造张量，形状为 (1, 1, 3)
    points = torch.tensor([[[x, y, z]]], dtype=torch.float32, device=device)
    return points

def numpy_to_torch_tensor(np_array, device):
    """
    将numpy 数组转换为指定 device 的 torch.Tensor
    """
    tensor = torch.from_numpy(np_array).to(device)
    return tensor