#!/usr/bin/env python3

"""
DINO Feature Extractor ROS2 Node

This node subscribes to RGB camera images and camera info topics,
extracts DINO features using the DinoFeatureExtractor class,
and publishes the extracted features as ImageFeatures messages.
Additionally publishes segmentation results as Image messages.

Author: <PERSON>liang Yin
Date: 2025-06-03
"""

import rclpy
from rclpy.node import Node
from rclpy.parameter import Parameter
from rcl_interfaces.msg import ParameterDescriptor
import torch
import numpy as np
import cv2

# ROS2 message imports
from sensor_msgs.msg import Image, CameraInfo
from std_msgs.msg import MultiArrayDimension
from loader_perception_msgs.msg import ImageFeatures

# Import DINO feature extractor
from dino_feature_extractor import DinoFeatureExtractor

# Import ROS image conversion utility
from .utils.ros_converter import (
    ros_image_to_torch, 
    numpy_to_ros_image, 
    download_image_from_url, 
    preprocess_image_for_dino
)


class DinoFeatureExtractorNode(Node):
    """
    ROS2 Node for extracting DINO features from camera images.
    
    This node subscribes to camera image and camera info topics,
    processes the images using a DINO feature extractor,
    and publishes the extracted features and segmentation results.
    Additionally publishes reference features once at startup.
    """
    
    def __init__(self):
        """Initialize the DINO feature extractor node."""
        super().__init__('dino_feature_extractor_node')
        
        # Flag to track camera info subscription status
        self._camera_info_received = False
        self._camera_info = None
        
        # Flag to track reference feature publication
        self._reference_feature_published = False
        
        # Initialize DINO feature extractor (will be set up after parameters are loaded)
        self._feature_extractor = None
        
        # Declare and get parameters with descriptors for better documentation
        self._declare_parameters()
        self._load_parameters()
        
        # Initialize DINO feature extractor with loaded parameters
        self._setup_feature_extractor()
        
        # Create subscribers and publishers
        self._setup_ros_interface()
        
        # Generate and publish reference features once
        self._generate_and_publish_reference_features()
        
        self.get_logger().info(f"DINO Feature Extractor Node initialized successfully")
        self.get_logger().info(f"Using device: {self._device}")
        self.get_logger().info(f"Using backbone: {self._backbone} ({self._backbone_type})")
        self.get_logger().info(f"Input size: {self._input_size}x{self._input_size}")
        self.get_logger().info(f"LoftUp sampler enabled: {self._use_loftup_sampler}")
        
    def _declare_parameters(self):
        """Declare all node parameters with descriptions and default values."""
        
        # Camera topics
        self.declare_parameter(
            'image_topic', 
            '/camera/image_raw',
            ParameterDescriptor(description='RGB camera image topic name')
        )
        
        self.declare_parameter(
            'camera_info_topic', 
            '/camera/camera_info',
            ParameterDescriptor(description='Camera info topic name')
        )
        
        # Output topics
        self.declare_parameter(
            'features_topic', 
            '/image_features',
            ParameterDescriptor(description='Output image features topic name')
        )
        
        self.declare_parameter(
            'reference_features_topic', 
            '/reference_features',
            ParameterDescriptor(description='Output reference features topic name')
        )
        
        self.declare_parameter(
            'segmentation_topic', 
            '/image_segmentation',
            ParameterDescriptor(description='Output segmentation image topic name')
        )
        
        # DINO model parameters
        self.declare_parameter(
            'device', 
            'cuda',
            ParameterDescriptor(description='Device to run the model on (cuda/cpu)')
        )
        
        self.declare_parameter(
            'backbone', 
            'dinov2',
            ParameterDescriptor(description='DINO backbone type (dino/dinov2)')
        )
        
        self.declare_parameter(
            'backbone_type', 
            'vit_base_reg',
            ParameterDescriptor(description='Specific ViT backbone type')
        )
        
        self.declare_parameter(
            'input_size', 
            448,
            ParameterDescriptor(description='Input image size for DINO model')
        )
        
        self.declare_parameter(
            'patch_size', 
            14,
            ParameterDescriptor(description='ViT patch size')
        )
        
        self.declare_parameter(
            'projection_type', 
            '',  # Empty string for None
            ParameterDescriptor(description='Projection head type (empty for None, "nonlinear" for nonlinear)')
        )
        
        self.declare_parameter(
            'dropout_p', 
            0.0,
            ParameterDescriptor(description='Dropout probability')
        )
        
        # Add LoftUp sampler parameter
        self.declare_parameter(
            'use_loftup_sampler', 
            True,
            ParameterDescriptor(description='Enable LoftUp sampler for feature interpolation')
        )
        
        # SLIC segmentation parameters
        self.declare_parameter(
            'slic_n_segments', 
            400,
            ParameterDescriptor(description='Number of SLIC segments')
        )
        
        self.declare_parameter(
            'slic_compactness', 
            30.0,
            ParameterDescriptor(description='SLIC compactness parameter')
        )
        
        # Reference texture image URLs
        self.declare_parameter(
            'reference_texture_urls', 
            ['https://ml-img.oss-cn-beijing.aliyuncs.com/texture1.jpg',
             'https://ml-img.oss-cn-beijing.aliyuncs.com/texture0.jpg'],
            ParameterDescriptor(description='URLs for reference texture images')
        )
        
    def _load_parameters(self):
        """Load all parameters from the parameter server."""
        
        # Camera topics
        self._image_topic = self.get_parameter('image_topic').get_parameter_value().string_value
        self._camera_info_topic = self.get_parameter('camera_info_topic').get_parameter_value().string_value
        self._features_topic = self.get_parameter('features_topic').get_parameter_value().string_value
        self._reference_features_topic = self.get_parameter('reference_features_topic').get_parameter_value().string_value
        self._segmentation_topic = self.get_parameter('segmentation_topic').get_parameter_value().string_value
        
        # DINO model parameters
        self._device = self.get_parameter('device').get_parameter_value().string_value
        self._backbone = self.get_parameter('backbone').get_parameter_value().string_value
        self._backbone_type = self.get_parameter('backbone_type').get_parameter_value().string_value
        self._input_size = self.get_parameter('input_size').get_parameter_value().integer_value
        self._patch_size = self.get_parameter('patch_size').get_parameter_value().integer_value
        self._use_loftup_sampler = self.get_parameter('use_loftup_sampler').get_parameter_value().bool_value
        
        # Handle projection_type (convert empty string to None)
        projection_type_str = self.get_parameter('projection_type').get_parameter_value().string_value
        self._projection_type = None if projection_type_str == '' else projection_type_str
        
        self._dropout_p = self.get_parameter('dropout_p').get_parameter_value().double_value
        
        # SLIC segmentation parameters
        self._slic_n_segments = self.get_parameter('slic_n_segments').get_parameter_value().integer_value
        self._slic_compactness = self.get_parameter('slic_compactness').get_parameter_value().double_value
        
        # Reference texture URLs
        self._reference_texture_urls = self.get_parameter('reference_texture_urls').get_parameter_value().string_array_value
        
        self.get_logger().info("Parameters loaded successfully:")
        self.get_logger().info(f"  Image topic: {self._image_topic}")
        self.get_logger().info(f"  Camera info topic: {self._camera_info_topic}")
        self.get_logger().info(f"  Features topic: {self._features_topic}")
        self.get_logger().info(f"  Reference features topic: {self._reference_features_topic}")
        self.get_logger().info(f"  Segmentation topic: {self._segmentation_topic}")
        self.get_logger().info(f"  Device: {self._device}")
        self.get_logger().info(f"  Backbone: {self._backbone} ({self._backbone_type})")
        self.get_logger().info(f"  LoftUp sampler: {self._use_loftup_sampler}")
        self.get_logger().info(f"  SLIC parameters: {self._slic_n_segments} segments, {self._slic_compactness} compactness")
        self.get_logger().info(f"  Reference texture URLs: {len(self._reference_texture_urls)} URLs")
        
    def _setup_feature_extractor(self):
        """Initialize the DINO feature extractor with loaded parameters."""
        try:
            self._feature_extractor = DinoFeatureExtractor(
                device=self._device,
                backbone=self._backbone,
                input_size=self._input_size,
                backbone_type=self._backbone_type,
                patch_size=self._patch_size,
                projection_type=self._projection_type,
                dropout_p=self._dropout_p,
                slic_n_segments=self._slic_n_segments,
                slic_compactness=self._slic_compactness,
                use_loftup_sampler=self._use_loftup_sampler  # Enable LoftUp sampler as requested
            )
            
            self.get_logger().info("DINO feature extractor initialized successfully")
            self.get_logger().info(f"Feature dimension: {self._feature_extractor.feature_dim}")
            
        except Exception as e:
            self.get_logger().error(f"Failed to initialize DINO feature extractor: {str(e)}")
            raise
            
    def _setup_ros_interface(self):
        """Set up ROS2 publishers and subscribers."""
        
        # Create publisher for image features
        self._features_publisher = self.create_publisher(
            ImageFeatures,
            self._features_topic,
            10
        )
        
        # Create publisher for reference features
        self._reference_features_publisher = self.create_publisher(
            ImageFeatures,
            self._reference_features_topic,
            10
        )
        
        # Create publisher for segmentation images (optional)
        # self._segmentation_publisher = self.create_publisher(
        #     Image,
        #     self._segmentation_topic,
        #     10
        # )
        
        # Create subscriber for camera info (only subscribe once)
        self._camera_info_subscriber = self.create_subscription(
            CameraInfo,
            self._camera_info_topic,
            self._camera_info_callback,
            10
        )
        
        # Create subscriber for RGB images
        self._image_subscriber = self.create_subscription(
            Image,
            self._image_topic,
            self._image_callback,
            10
        )
        
        self.get_logger().info("ROS2 interface set up successfully")
        self.get_logger().info(f"Subscribed to: {self._image_topic}")
        self.get_logger().info(f"Subscribed to: {self._camera_info_topic}")
        self.get_logger().info(f"Publishing features to: {self._features_topic}")
        self.get_logger().info(f"Publishing reference features to: {self._reference_features_topic}")
        self.get_logger().info(f"Publishing segmentation to: {self._segmentation_topic}")
        
    def _camera_info_callback(self, msg: CameraInfo):
        """
        Callback function for camera info messages.
        Only processes the first message to avoid repeated subscriptions.
        
        Args:
            msg (CameraInfo): Camera information message
        """
        if not self._camera_info_received:
            self._camera_info = msg
            self._camera_info_received = True
            
            self.get_logger().info("Camera info received successfully")
            self.get_logger().info(f"Camera resolution: {msg.width}x{msg.height}")
            
            # Destroy the camera info subscriber to avoid repeated subscriptions
            self.destroy_subscription(self._camera_info_subscriber)
            self.get_logger().info("Camera info subscriber destroyed to avoid repetition")
    
    @torch.no_grad()
    def _generate_and_publish_reference_features(self):
        """
        Generate reference features from texture images and publish once.
        This method downloads texture images, extracts their patch-level features,
        and computes a reference feature by averaging.
        
        Note: Uses minimal preprocessing since DinoFeatureExtractor handles 
        comprehensive preprocessing internally (resizing, normalization, etc.)
        """
        if self._reference_feature_published:
            return
            
        try:
            self.get_logger().info("Generating reference features from texture images...")
            
            texture_features = []
            successful_downloads = 0
            
            # Download and process each reference texture image
            for i, url in enumerate(self._reference_texture_urls):
                self.get_logger().info(f"Processing reference texture image {i+1}/{len(self._reference_texture_urls)}")
                
                # Download image using unified converter function
                image = download_image_from_url(url, timeout=10)
                if image is None:
                    self.get_logger().warn(f"Failed to download texture image {i+1}, skipping...")
                    continue
                    
                # Preprocess image using unified converter function (minimal preprocessing)
                # DinoFeatureExtractor will handle resizing, normalization internally
                image_tensor = preprocess_image_for_dino(image, device=self._feature_extractor._device)
                
                # Extract features using the same method as test_texture.py
                patch_features = self._feature_extractor.extract_features(image_tensor)
                
                # Average pool patch features to get global representation
                # patch_features shape: [B, num_patches, C] -> [B, C]
                pooled_features = patch_features.mean(dim=1)
                texture_features.append(pooled_features)
                successful_downloads += 1
                
                # self.get_logger().info(f"Extracted features for texture {i+1}: {patch_features.shape} -> {pooled_features.shape}")
            
            if successful_downloads == 0:
                self.get_logger().error("Failed to download any reference texture images")
                return
                
            # Compute reference feature by averaging all texture features
            reference_feature = torch.mean(torch.stack(texture_features), dim=0)  # [B, C]
            
            self.get_logger().info(f"Generated reference feature shape: {reference_feature.shape}")
            self.get_logger().info(f"Reference feature computed from {successful_downloads} texture images")
            
            # Create and publish reference features message
            self._publish_reference_features(reference_feature)
            self._reference_feature_published = True
            
            self.get_logger().info("Reference features published successfully")
            
        except Exception as e:
            self.get_logger().error(f"Error generating reference features: {str(e)}")
    
    def _publish_reference_features(self, reference_feature: torch.Tensor):
        """
        Publish reference features as ImageFeatures message.
        
        Args:
            reference_feature (torch.Tensor): Reference feature tensor of shape [B, C]
        """
        try:
            # Create ImageFeatures message
            msg = ImageFeatures()
            
            # Set header with current time
            msg.header.stamp = self.get_clock().now().to_msg()
            msg.header.frame_id = "reference_frame"
            
            # Convert features to numpy array
            feat_np = reference_feature.cpu().numpy()
            
            self.get_logger().info(f"Reference features numpy shape: {feat_np.shape}")
            
            # Set MultiArray dimensions information
            # Dimension 0: batch size
            dim1 = MultiArrayDimension()
            dim1.label = "batch"
            dim1.size = feat_np.shape[0]
            dim1.stride = feat_np.shape[0] * feat_np.shape[1]
            
            # Dimension 1: feature dimension
            dim2 = MultiArrayDimension()
            dim2.label = "feat_dim"
            dim2.size = feat_np.shape[1]
            dim2.stride = feat_np.shape[1]
            
            # Set features data and layout
            msg.features.data = feat_np.flatten().tolist()
            msg.features.layout.dim = [dim1, dim2]
            
            # Publish the message
            self._reference_features_publisher.publish(msg)
            
            self.get_logger().info(f"Published reference features with shape {feat_np.shape}")
            
        except Exception as e:
            self.get_logger().error(f"Error publishing reference features: {str(e)}")
    
    @torch.no_grad()     
    def _image_callback(self, msg: Image):
        """
        Callback function for camera image messages.
        Processes the image, extracts DINO features and computes segmentation.
        
        Args:
            msg (Image): ROS2 Image message
        """
        try:
            # Convert ROS image to PyTorch tensor using the utility function
            # This handles the conversion from ROS Image to (C, H, W) format tensor
            img_tensor = ros_image_to_torch(
                ros_img=msg, 
                desired_encoding='rgb8', 
                device=self._feature_extractor._device
            )
            
            # Add batch dimension: (C, H, W) -> (1, C, H, W)
            img_batch = img_tensor.unsqueeze(0)
            
            self.get_logger().debug(f"Input image tensor shape: {img_batch.shape}")
            
            # Extract DINO features and compute segmentation using the updated method
            # Returns: patch_features, dense_features, sparse_features, edges, seg, centers
            patch_features, dense_features, sparse_features, edges, seg, centers = self._feature_extractor.extract_features_and_segments(img_batch)
            
            self.get_logger().debug(f"Extracted patch features shape: {patch_features.shape}")
            self.get_logger().debug(f"Extracted dense features shape: {dense_features.shape}")
            self.get_logger().debug(f"Extracted sparse features shape: {sparse_features.shape}")
            self.get_logger().debug(f"Segmentation shape: {seg.shape}")
            self.get_logger().debug(f"Number of edges: {edges.shape[1]}")
            self.get_logger().debug(f"Number of segment centers: {centers.shape[0]}")
            
            # Publish the extracted sparse features (as requested in the modification)
            self._publish_features(msg, sparse_features)
            
            # Remove segmentation publishing as requested
            # self._publish_segmentation(msg, seg)
            
        except Exception as e:
            self.get_logger().error(f"Error processing image: {str(e)}")
            
    def _publish_features(self, image_msg: Image, sparse_features: torch.Tensor):
        """
        Publish extracted sparse features as ImageFeatures message.
        
        Args:
            image_msg (Image): Original image message for header information
            sparse_features (torch.Tensor): Extracted sparse features tensor of shape [num_segments, C]
        """
        try:
            # Create ImageFeatures message
            msg = ImageFeatures()
            msg.header = image_msg.header
            
            # Convert features to numpy array
            feat_np = sparse_features.cpu().numpy()
            
            self.get_logger().debug(f"Sparse features numpy shape: {feat_np.shape}")
            
            # Set MultiArray dimensions information
            # Dimension 0: number of segments
            dim1 = MultiArrayDimension()
            dim1.label = "num_segments"
            dim1.size = feat_np.shape[0]
            dim1.stride = feat_np.shape[0] * feat_np.shape[1]
            
            # Dimension 1: feature dimension
            dim2 = MultiArrayDimension()
            dim2.label = "feat_dim"
            dim2.size = feat_np.shape[1]
            dim2.stride = feat_np.shape[1]
            
            # Set features data and layout
            msg.features.data = feat_np.flatten().tolist()
            msg.features.layout.dim = [dim1, dim2]
            
            # Publish the message
            self._features_publisher.publish(msg)
            
            self.get_logger().debug(f"Published sparse features with {feat_np.shape[0]} segments, "
                                   f"each with {feat_np.shape[1]} dimensions")
            
        except Exception as e:
            self.get_logger().error(f"Error publishing features: {str(e)}")
            
    def _publish_segmentation(self, image_msg: Image, seg: torch.Tensor):
        """
        Publish segmentation result as Image message.
        
        Args:
            image_msg (Image): Original image message for header information
            seg (torch.Tensor): Segmentation tensor of shape (H, W) with segment IDs
        """
        try:
            # Convert segmentation tensor to numpy array
            seg_np = seg.cpu().numpy().astype(np.uint16)
            
            self.get_logger().debug(f"Segmentation numpy shape: {seg_np.shape}")
            self.get_logger().debug(f"Segmentation value range: {seg_np.min()} - {seg_np.max()}")
            
            # Normalize segmentation to 0-255 range for visualization
            seg_normalized = ((seg_np - seg_np.min()) * 255 / (seg_np.max() - seg_np.min())).astype(np.uint8)
            
            # Apply colormap for better visualization
            seg_colored = cv2.applyColorMap(seg_normalized, cv2.COLORMAP_JET)
            
            # Convert to ROS Image message using utility function
            seg_msg = numpy_to_ros_image(seg_colored, desired_encoding="bgr8")
            seg_msg.header = image_msg.header
            
            # Publish the segmentation message
            self._segmentation_publisher.publish(seg_msg)
            
            self.get_logger().debug(f"Published segmentation with {np.unique(seg_np).shape[0]} unique segments")
            
        except Exception as e:
            self.get_logger().error(f"Error publishing segmentation: {str(e)}")


def main(args=None):
    """Main function to run the DINO feature extractor node."""
    
    # Initialize ROS2
    rclpy.init(args=args)
    
    try:
        # Create and run the node
        node = DinoFeatureExtractorNode()
        
        node.get_logger().info("DINO Feature Extractor Node is running...")
        
        # Spin the node
        rclpy.spin(node)
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Error running node: {str(e)}")
    finally:
        # Clean shutdown
        if 'node' in locals():
            node.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
